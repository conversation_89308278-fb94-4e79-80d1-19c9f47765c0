import { Flex } from "@chakra-ui/react";
import React, { useCallback, useMemo, useState } from "react";

interface FloatButtonProps {
  mainIcon: JSX.Element;
  flipIcon?: JSX.Element;
  color?: string;
  title?: string;
  opacity?: number;
  onClick?: () => void;
}

export default function FloatButton({
  mainIcon,
  flipIcon,
  color,
  title,
  opacity,
  onClick,
}: FloatButtonProps) {
  const [toggle, setToggle] = useState(false);

  const IconElement = useMemo(() => {
    if (flipIcon && toggle) {
      return flipIcon;
    }
    return mainIcon;
  }, [mainIcon, flipIcon, toggle]);

  const handleClick = useCallback(() => {
    setToggle((prev) => !prev);
    onClick && onClick();
  }, [onClick]);

  return (
    <Flex
      title={title}
      w="40px"
      h="40px"
      align="center"
      justify="center"
      rounded="full"
      pos="relative"
      sx={{ cursor: "pointer" }}
      _hover={{
        opacity: "0.8",
      }}
      onClick={handleClick}
    >
      <Flex
        bg={color ? color : "#fff"}
        w="40px"
        h="40px"
        align="center"
        justify="center"
        rounded="full"
        opacity={opacity ? opacity : 0.5}
        sx={{ cursor: "pointer" }}
      ></Flex>
      {IconElement}
    </Flex>
  );
}
