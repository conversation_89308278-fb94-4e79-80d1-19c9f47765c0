import { Flex, H<PERSON><PERSON>ck, Icon, Td, Text, Tr } from "@chakra-ui/react";
import { FC } from "react";
import { format, parseISO } from "date-fns";
import { ButtonApprove } from "~/components/global/Buttons/ButtonApprove";
import { BsCheck2All } from "react-icons/bs";
import { GiCancel } from "react-icons/gi";

type CardAppointmentDoctorProps = {
  secure_id: string;
  date: string;
  patient: string;
  type: string;
  specialtyOrExam: string;
  status: string;
  handleSubmitVideoCallLobby: () => Promise<void>;
};

const getStatus = (status: string) => {
  switch (status) {
    case "waiting":
      return "Aguardando";

    case "realized":
      return "Realizado";

    case "canceled":
      return "Cancelada";

    default:
      break;
  }
};

export const CardAppointmentDoctor: FC<CardAppointmentDoctorProps> = ({
  secure_id,
  date,
  patient,
  type,
  specialtyOrExam,
  status,
  handleSubmitVideoCallLobby,
}) => {
  return (
    <Tr>
      <Td>
        <Text fontSize="sm">
          {format(parseISO(date), "dd/MM/yyyy")} às{" "}
          {format(parseISO(date), "HH:mm")}
        </Text>
      </Td>
      <Td>
        <Text fontSize="sm">{patient}</Text>
      </Td>
      <Td>
        <Text fontSize="sm">
          {type === "exam"
            ? "Exame"
            : type === "in_person"
            ? "Cons. Presencial"
            : "Vídeo Chamada"}
        </Text>
      </Td>
      <Td>
        <Text fontSize="sm">{specialtyOrExam}</Text>
      </Td>
      <Td>
        <Text>{getStatus(status)}</Text>
      </Td>
      <Td>
        <Flex justify="flex-end">
          <HStack>
            {status === "waiting" && (
              <ButtonApprove
                handleConfirm={handleSubmitVideoCallLobby}
                message="Deseja realizar consulta?"
                titlePopover="Realizar consulta"
                tooltipLabel="Realizar"
                confirmTitle="Confirmar"
              />
            )}
            {status !== "waiting" && (
              <Flex
                fontSize="sm"
                bg="gray"
                w="44px"
                h="32px"
                align="center"
                justify="center"
                rounded="0.375rem"
                title={getStatus(status)}
              >
                <Icon
                  as={status === "realized" ? BsCheck2All : GiCancel}
                  fontSize="20"
                  color="#fff"
                />
              </Flex>
            )}
          </HStack>
        </Flex>
      </Td>
    </Tr>
  );
};
