import {
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ButtonAlterStatus } from "~/components/global/Buttons/ButtonAlterStatus"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"

type CardUserAdminProps = {
	secure_id: string
	name: string
	email: string
	cell?: string
	legal_document_number?: string
}

export const CardUserAdmin: FC<CardUserAdminProps> = ({ secure_id, name, email, legal_document_number, cell }) => {
	const toast = useToast()

	const userCanSeeDelete = useCan({
		permissions: ['users_delete']
	})

	const userCanSeeEdit = useCan({
		permissions: ['users_edit']
	})

	const deleteUser = useMutation(async () => {
		return await api.delete(`/v1/admin/users/${secure_id}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['UsersAdmin'])
			toast({
				title: response.data?.message || 'Usuário apagado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao apagar usuário.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{email}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{legal_document_number}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{cell}</Text>
			</Td>
			{(userCanSeeDelete || userCanSeeEdit) && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{userCanSeeEdit && (
								<ButtonToEditItemList
									tooltipLabel="Editar usuário"
									linkHref={`/admin/users/${secure_id}`}
								/>
							)}
							{userCanSeeDelete && (
								<ButtonDelete
									deleteFunction={deleteUser}
									titlePopover="Remover usuário"
									tooltipLabel="Remover usuário"
									message="Deseja remover esse usuário?"
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
