import { Checkbox, Divider, VStack } from '@chakra-ui/react'
import { Dispatch, FC, SetStateAction, useEffect, useState } from "react"
import { UseFormSetValue } from 'react-hook-form'
import { PermissionProps } from '~/utils/Types/Permissions'

interface PermissionsCardAdminProps {
	name: string
	permissions: PermissionProps[]
	permissionsDefault: {
		slug?: string
		description?: string
		name?: string
	}[]
	setValue: UseFormSetValue<any>
	positionIndexCard: number
	globalCheckedItems: any[]
	setGlobalCheckedItems: Dispatch<SetStateAction<any[]>>
	checkedAll: boolean | undefined
	setCheckedAll: Dispatch<SetStateAction<boolean | undefined>>
}

export const PermissionsCardAdmin: FC<PermissionsCardAdminProps> = ({
	name, permissions, permissionsDefault, setValue,
	globalCheckedItems, positionIndexCard, setGlobalCheckedItems,
	checkedAll, setCheckedAll
}) => {
	const [checkedItems, setCheckedItems] = useState(permissions.map(permission => false))

	useEffect(() => {
		if (checkedAll === true) {
			setCheckedItems(permissions.map(permission => true))
		} else if (checkedAll === false) {
			setCheckedItems(permissions.map(permission => false))
		}
	}, [checkedAll])

	const allChecked = checkedItems.every(Boolean)
	const isIndeterminate = checkedItems.some(Boolean) && !allChecked

	useEffect(() => {
		setCheckedItems(permissions.map((permission, indexEffect) =>
			(!!permissionsDefault[0] && permissionsDefault.filter(permissionDefault =>
				permissionDefault.slug === permission.slug
			)[0]) ? true : false
		))
		permissions.forEach((permission, indexEffect) => {
			if (!!permissionsDefault[0] && permissionsDefault.filter(permissionDefault =>
				permissionDefault.slug === permission.slug
			)[0]) {
				setValue(`permissions.${permission.slug}`, permission.slug)
				setGlobalCheckedItems(
					globalCheckedItems.map(
						(permission, newIndex) =>
							newIndex === positionIndexCard
								? true
								: globalCheckedItems[newIndex]
					))
				setCheckedAll(undefined)
			}
		})
	}, [])

	return (
		<VStack align="flex-start">
			<Checkbox
				name={`group.${name}`}
				isChecked={allChecked}
				isIndeterminate={isIndeterminate}
				onChange={(e) => {
					if (e.target.checked) {
						permissions.forEach(permission => setValue(`permissions.${permission.slug}`, permission.slug))
					} else {
						permissions.forEach(permission => setValue(`permissions.${permission.slug}`, null))
					}
					setGlobalCheckedItems(
						globalCheckedItems.map(
							(permission, newIndex) =>
								newIndex === positionIndexCard
									? e.target.checked
									: globalCheckedItems[newIndex]
						))
					setCheckedItems(permissions.map(permission => e.target.checked))
					setCheckedAll(undefined)
				}}
				alignSelf="left"
				fontSize="lg"
				fontWeight="bold"
				textColor="textMD"
			>
				{name}
			</Checkbox>
			<Divider my="6" borderColor="gray.50" />
			{permissions.map((permission, index) => (
				<Checkbox
					key={permission.slug}
					name={`permissions.${permission.slug}`}
					value={permission.slug}
					isChecked={checkedItems[index]}
					onChange={(e) => {
						if (e.target.checked) {
							setValue(`permissions.${permission.slug}`, permission.slug)
						} else {
							setValue(`permissions.${permission.slug}`, null)
						}
						setCheckedItems(
							permissions.map((permission, newIndex) => newIndex === index ? e.target.checked : checkedItems[newIndex])
						)
						setGlobalCheckedItems(
							globalCheckedItems.map(
								(permission, newIndex) =>
									newIndex === positionIndexCard
										? e.target.checked
										: globalCheckedItems[newIndex]
							))
						setCheckedAll(undefined)
					}}
				>
					{permission.description}
				</Checkbox>
			))}
		</VStack>
	)
}
