import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner, <PERSON><PERSON><PERSON>ck,
  I<PERSON>
} from '@chakra-ui/react';
import { LuBell, LuBellDot } from 'react-icons/lu';
import { useImportNotifications } from '~/hooks/Admin/ImportJobs/useImportNotifications';

interface NotificationBellProps {
  onNotificationClick: (jobId: number, partnerId?: string) => void;
}

export const NotificationBell = ({ onNotificationClick }: NotificationBellProps) => {
  const { summary, hasNotifications, isLoading, markAsRead } = useImportNotifications();

  const handleItemClick = (job: any) => {
    const partnerId = job?.summary?.partnerId
      ?? job?.summary?.partner_id
      ?? job?.summary?.partnerSecureId
      ?? job?.summary?.partner_secure_id;
    onNotificationClick(job.id, partnerId);

    // Se o job já terminou, marca como lido
    if (job.status !== 'pending' && job.status !== 'processing') {
      markAsRead.mutate(job.id);
    }
  };

  return (
    <Menu>
      <MenuButton>
        <Icon
          as={hasNotifications ? LuBellDot : LuBell }
          fontSize="24"
          cursor="pointer"
          color="secondaryText"
        />
      </MenuButton>
      <MenuList>
        {isLoading && <Spinner mx="auto" display="block" />}
        {!isLoading && !hasNotifications && <MenuItem>Nenhuma notificação</MenuItem>}
        
        {/* Jobs em Andamento */}
        {summary?.processingJobs?.map((job: any) => (
          <MenuItem key={job.id} onClick={() => handleItemClick(job)}>
            <VStack align="start" spacing={0}>
              <Text fontWeight="bold">Importação em Andamento</Text>
              <Text fontSize="sm">Arquivo: {job.original_filename}</Text>
            </VStack>
          </MenuItem>
        ))}

        {/* Jobs Finalizados */}
        {summary?.recentFinishedJobs?.map((job: any) => (
          <MenuItem key={job.id} onClick={() => handleItemClick(job)}>
             <VStack align="start" spacing={0}>
              <Text fontWeight="bold" color={job.status === 'completed' ? 'green.500' : 'red.500'}>
                Importação {job.status === 'completed' ? 'Concluída' : 'Falhou'}
              </Text>
              <Text fontSize="sm">Arquivo: {job.original_filename}</Text>
            </VStack>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};