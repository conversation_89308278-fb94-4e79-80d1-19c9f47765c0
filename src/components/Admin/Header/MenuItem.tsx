import { MenuItem as <PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@chakra-ui/react'
import { FC, ReactNode } from "react"

interface MenuItemProps {
	onClick?: () => void
	children?: ReactNode
}

export const MenuItem: FC<MenuItemProps> = ({ children, onClick }) => {
	return (
		<MenuItemChakra
			fontWeight="400"
			fontSize="md"
			justifyContent="center"
			onClick={onClick}
		>
			{children}
		</MenuItemChakra>
	)
}
