import {
	Flex,
	<PERSON><PERSON><PERSON>ck,
	<PERSON>dal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	useToast
} from "@chakra-ui/react"
import { FC, useEffect, useMemo, useState } from "react"

import { parse } from "date-fns"

import * as yup from 'yup'
import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { read, utils } from 'xlsx'

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { typesOfCares } from "~/utils/Types/Global"

import { InputDocFile } from "~/components/global/Form/InputDocFile"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { validateLocaleAndSetLanguage } from "typescript"

// Labels compatíveis para planilhas
export const typesDoctors = [
	{ value: 'doctor', label: 'MEDICO' },
	{ value: 'clinic', label: 'CLINICA' },
	{ value: 'hospital', label: 'HOSPITAL' },
	{ value: 'lab', label: 'LABORATORIO' },
]

const FormSchema = yup.object().shape({
	file: yup.mixed().required('Você deve importar o arquivo Excel'),
	// users: yup.array().of(yup.object({
	// 	name: yup.string().required(),
	// 	email: yup.string().required(),
	// 	legalDocumentNumber: yup.string().required(),
	// 	dddCell: yup.number().required(),
	// 	cell: yup.number().required(),
	// 	birthDate: yup.string().required(),
	// 	adviceRegister: yup.string().required(),
	// 	paymentMethods: yup.string().required(),
	// 	typeOfCare: yup.mixed<'in_person' | 'video_call' | 'both'>().oneOf(['in_person', 'video_call', 'both']).required(),
	// 	type: yup.mixed<'doctor' | 'clinic' | 'hospital' | 'lab'>().oneOf(['doctor', 'clinic', 'hospital', 'lab']).required()
	// }).required()).optional()
})

type FormData = {
	file: File
	users?: {
		name: string
		email: string
		legalDocumentNumber: string
		dddCell: number
		cell: number
		birthDate: string
		password?: string
		adviceRegister: string
		paymentMethods: string
		typeOfCare: 'in_person' | 'video_call' | 'both'
		type: 'doctor' | 'clinic' | 'hospital' | 'lab'
    zipCode: string
    street: string
    number: string
    complement: string
    neighborhood: string
    city: string
    state: string
    queryValue: number
    specialtiesNames: string
    examsNames: string
    holderLegalDocumentNumber?: string
	}[]
}

interface ModalImportAccreditedProps {
	isOpen: boolean
	closeModal: () => void
}

export const ModalImportAccredited: FC<ModalImportAccreditedProps> = ({ closeModal, isOpen }) => {
	const toast = useToast()

  const [hasErrors, setHasErrors] = useState(false)
	const [isProcessing, setIsProcessing] = useState(false)

	const { handleSubmit, register, formState, watch, control, reset, setValue } = useForm<FormData>({
		resolver: yupResolver<any>(FormSchema),
		defaultValues: {
		}
	})
	const { errors } = formState

	const file = watch("file")
	const users = watch("users")

	const readExcel = (fileRead: File) => {
		setIsProcessing(true)
		setValue("users", [])

		const worker = new Worker(new URL('~/utils/workers/excelAccreditedsWorker.ts', import.meta.url))

		worker.onmessage = (event) => {
			const { success, data, error } = event.data;

			if (success) {
				const finalData = data.map((user: any) => ({
            ...user,
            birthDate: user.birthDate && typeof user.birthDate === 'string' ? new Date(user.birthDate) : user.birthDate,
        }));
        setValue("users", finalData);
			} else {
				toast({
          title: "Erro ao processar o arquivo",
          description: error,
          status: "error",
          position: "top-right",
          isClosable: true,
        });
			}
			setIsProcessing(false); // Finaliza o loading
      worker.terminate(); // Encerra o worker
		};

		worker.onerror = (error) => {
      toast({
        title: "Erro crítico no worker",
        description: error.message,
        status: "error",
        position: "top-right",
        isClosable: true,
      });
      setIsProcessing(false);
      worker.terminate();
    };

		worker.postMessage(fileRead);
	}

	const dataExportExcel = useMemo(() => {
		return new Array(1).fill(null).map((_, index) => ({
			'Nome': `Usuário`,
			'E-mail': `usuario${index + 1}@exemplo.com.br`,
			'CNPJ/CPF': `000.000.000-00`,
			'Celular': '(00)00000-0000',
			'Data de Nascimento/Fundação (##/##/####)': '##/##/####',
			'Registro': '123',
			'CEP': '321',
			'Endereço': 'Avenida',
			'Número': '123',
			'Complemento': '',
			'Bairro': 'Bairro',
			'Cidade': 'Cidade',
			'Estado': 'Estado',
			'Métodos de Pagamento': 'método1,método2',
			'Tipo de Atendimento (Presencial, Vídeo, Ambos)': 'Presencial',
			'Tipo (Médico, Clínica, Hospital, Laboratório)': 'MEDICO',
			'Valor': '150,00',
			'Especialidades (Separadas por vírgula)': 'Psicologia, Fonoaudiologia, Fisioterapia, Ultrassonografia',
			'Exames (Separados por vírgula)': 'Ultrassonografia, Ressonancia Magnetica',
			'Senha (Não obrigatório)': 'Não obrigatório'
		}))
	}, [])

  const excelErrors = useMemo(() => {
  if (users) {
    setHasErrors(false);
    const legalDocumentNumbers = new Set();
    let anyErrorFound = false;

    const errorsData = users.map(user => {
      const userErrors: string[] = [];
      // const cleanedLegalDocument = user.legalDocumentNumber?.replace(/\D/g, '');

      if (!user.name) userErrors.push('Nome é obrigatório.');
      if (!user.email) userErrors.push('E-mail é obrigatório.');
      if (!user.legalDocumentNumber) userErrors.push('CNPJ/CPF é obrigatório.');

      const fullCellNumber = `${user.dddCell || ''}${user.cell || ''}`;
      if (!fullCellNumber) {
        userErrors.push('Celular é obrigatório.');
      } else if (fullCellNumber.length < 10) { 
        userErrors.push('Celular inválido ou incompleto.');
      } else if (fullCellNumber.length > 11) {
        userErrors.push('Celular ultrapassa o limite de dígitos.');
      }

      if (!user.typeOfCare) {
        userErrors.push('Tipo de Atendimento é obrigatório.');
      } else if (!typesOfCares.some(t => t.label === user.typeOfCare)) {
        userErrors.push(`Tipo de Atendimento "${user.typeOfCare}" é inválido.`);
      }

      if (!user.type) {
        userErrors.push('Tipo (MEDICO, CLINICA...) é obrigatório.');
      } else if (!typesDoctors.some(t => t.label === user.type)) {
        userErrors.push(`Tipo (MEDICO...) "${user.type}" é inválido.`);
      }
      
      if (user.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
        userErrors.push('Formato de e-mail inválido.');
      }

      // if (cleanedLegalDocument && cleanedLegalDocument.length !== 11 && cleanedLegalDocument.length !== 14) {
      //   userErrors.push('CNPJ/CPF com quantidade de dígitos inválida.');
      // }

			// NOTE: Acredito que não tem necessidade de validar duplicidade de CNPJ/CPF por conta das filiais
      // if (cleanedLegalDocument && legalDocumentNumbers.has(cleanedLegalDocument)) {
      //   userErrors.push('CNPJ/CPF duplicado neste arquivo.');
      // } else if(cleanedLegalDocument) {
      //   legalDocumentNumbers.add(cleanedLegalDocument);
      // }

      if (userErrors.length > 0) {
        anyErrorFound = true;
      }

      return {
        'Nome': user.name,
        'E-mail': user.email,
        'CNPJ/CPF': user.legalDocumentNumber,
        'Celular': user.dddCell && user.cell ? `${user.dddCell}${user.cell}` : '',
        'Data de Nascimento/Fundação (##/##/####)': user.birthDate,
        'Registro': user.adviceRegister,
        'CEP': user.zipCode,
        'Endereço': user.street,
        'Número': user.number,
        'Complemento': user.complement,
        'Bairro': user.neighborhood,
        'Cidade': user.city,
        'Estado': user.state,
        'Métodos de Pagamento': user.paymentMethods,
        'Tipo de Atendimento (Presencial, Vídeo, Ambos)': user.typeOfCare,
        'Tipo (Médico, Clínica, Hospital, Laboratório)': user.type,
        'Valor': user.queryValue,
        'Especialidades (Separadas por vírgula)': user.specialtiesNames,
        'Exames (Separados por vírgula)': user.examsNames,
        'Senha (Não obrigatório)': user.password,
        'Erros': userErrors.join(' | '), // Junta todos os erros em uma única string
      };
    });

    // Atualiza o estado de erro global se algum erro foi encontrado
    if (anyErrorFound) {
      setHasErrors(true);
    }
    
    // Filtra para mostrar apenas as linhas que contêm erros
    return errorsData
  }

  return [];
}, [users]);

  useEffect(() => {
      if (hasErrors) {
        toast({
          title: `Erro na importação!`,
          position: "top-right",
          status: "error",
          isClosable: true,
        })
      }
    }, [hasErrors])

	const add = useMutation(async (values: FormData) => {
		return await api.post(`/v1/admin/import-accrediteds`, {
			users: values.users,
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['AccreditedsAdmin'])
			handleCloseModal()
			toast({
				title: response.data?.message || `Credenciados importados com sucesso!`,
				position: "top-right",
				status: "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || error?.response?.data?.errors[0].message || 'Ocorreu um problema ao importar credenciados.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleAdd: SubmitHandler<FormData> = async (values) => {
    if (!values.users) {
    toast({
      title: "Nenhum usuário para importar.",
      status: "warning",
      position: "top-right",
      isClosable: true,
    });
    return;
  }

    const payload: FormData = {
    ...values,
    users: values.users.map(user => ({
      ...user,
      typeOfCare: typesOfCares.find(t => t.label === user.typeOfCare)?.value as 'in_person' | 'video_call' | 'both',
      type: typesDoctors.find(t => t.label === user.type)?.value as 'doctor' | 'clinic' | 'hospital' | 'lab',
    }))
  };
		try {
			await add.mutateAsync(payload)
		} catch {}
	}

	const handleCloseModal = () => {
		closeModal()
		reset()
	}

	useEffect(() => {
		if (file) {
			readExcel(file)
		}
	}, [file])

	return (
		<Modal size="md" isOpen={isOpen} onClose={handleCloseModal} closeOnOverlayClick={false}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Importar</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						as="form"
						width="100%"
						p="4"
						spacing={["6", "8"]}
						flexDirection="column"
						align="flex-start"
						justify="center"
						onSubmit={handleSubmit(handleAdd)}
					>
						<ButtonExportExcel
							variant="link"
							isDisabled={dataExportExcel.length === 0}
							data={dataExportExcel}
							fileName={`modelo_importar_credenciado`}
						>
							Exportar modelo
						</ButtonExportExcel>
						<InputDocFile
							isDisabled={isProcessing}
							watch={watch}
							control={control}
							error={errors.file}
							{...register('file')}
						/>

          	{isProcessing && <p>Processando planilha, por favor aguarde...</p>}

            {users && hasErrors ?
							<ButtonExportExcel
								variant="link"
								isDisabled={excelErrors.length === 0}
								data={excelErrors}
								fileName={`${file?.name.replace('.xlsx', '')}_erros`}
							>
								Tabela com erros
							</ButtonExportExcel>
						: null}
						<Flex justify="flex-end" w="100%">
							<HStack spacing="4" width="20em">
								<ButtonCancelSubmit onClick={handleCloseModal}>Cancelar</ButtonCancelSubmit>
								<ButtonSubmit isLoading={formState.isSubmitting || isProcessing} isDisabled={!users || hasErrors || isProcessing}>Salvar</ButtonSubmit>
							</HStack>
						</Flex>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}