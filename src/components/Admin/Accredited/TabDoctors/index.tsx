import {
  <PERSON>,
  <PERSON>lex,
  <PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  <PERSON>,
  TableContainer,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  VStack,
} from "@chakra-ui/react";

import { useCan } from "~/hooks/useCan";
import { useControlFilters } from "~/contexts/ControlFiltersContext";

import { Pagination } from "~/components/global/Pagination";
import { InputSearch } from "~/components/global/Form/InputSearch";
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate";
import { CardDoctorAdmin } from "./CardDoctorAdmin";
import { useClinics } from "~/hooks/Admin/Clinics/useClinics";

interface TabDoctorProps {
  clinicId: string;
}

export function TabDoctors({ clinicId }: TabDoctorProps) {
  const { page, limit, search, setPage, setLimit, setSearch } =
    useControlFilters();

  const { data, isLoading, error, isFetching } = useClinics({
    page,
    search,
    limit,
    clinicId,
  });

  const userCanSeeCreate = useCan({
    permissions: ["accrediteds_create"],
  });

  const userCanSeeEdit = useCan({
    permissions: ["accrediteds_edit"],
  });

  const userCanSeeDelete = useCan({
    permissions: ["accrediteds_delete"],
  });

  return (
    <VStack spacing="4">
      <VStack width="100%" p="4" align="flex-end">
        {userCanSeeCreate && (
          <ButtonToCreate linkHref={`/admin/accrediteds/${clinicId}/doctors/add`}>
            Novo
          </ButtonToCreate>
        )}
        <Flex w="100%" justify="space-between">
          <Flex>
            {!!error && (
              <Flex justify="center">
                <Text>Falha ao obter dados.</Text>
              </Flex>
            )}
          </Flex>
          <HStack spacing="4" align="center">
            {isFetching && !isLoading && <Spinner />}
            <Box w="72">
              <InputSearch
                name="search"
                placeholder="Nome"
                setPage={setPage}
                setSearch={setSearch}
              />
            </Box>
          </HStack>
        </Flex>
        {data && (
          <>
            <TableContainer w="100%">
              <Table>
                <Thead>
                  <Tr>
                    <Th>Nome</Th>
                    {(userCanSeeEdit || userCanSeeDelete) && (
                      <Th>
                        <Text align="center">Ações</Text>
                      </Th>
                    )}
                  </Tr>
                </Thead>
                <Tbody>
                  {data.doctors.map((doctor) => (
                    <CardDoctorAdmin
                      key={doctor.secure_id}
                      clinicId={clinicId}
                      name={doctor.userInfo?.name}
                      secureId={doctor.secure_id}
                    />
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
            <Flex justify="flex-end" w="100%">
              <Pagination
                totalCountOfRegisters={data.total}
                registersInCurrentPage={data.doctors.length}
                currentPage={data.page}
                registersPerPage={data.perPage}
                onPageChange={setPage}
                limit={limit}
                setLimit={setLimit}
              />
            </Flex>
          </>
        )}
      </VStack>
    </VStack>
  );
};
