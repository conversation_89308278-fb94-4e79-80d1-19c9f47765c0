import {
	Checkbox,
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { <PERSON>sp<PERSON>, FC, SetStateAction } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"
import { ButtonAlterStatus } from "~/components/global/Buttons/ButtonAlterStatus"
import { getUserStatus } from "~/utils/translateNames/user/status"

type CardAccreditedAdminProps = {
	accredited: ListUserAdmin
	checked: boolean
	setSelectedItems: Dispatch<SetStateAction<string[]>>
}

export const CardAccreditedAdmin: FC<CardAccreditedAdminProps> = ({ accredited, checked, setSelectedItems }) => {
	const toast = useToast()

	const userCanSeeDelete = useCan({
		permissions: ['accrediteds_delete']
	})

	const userCanSeeEdit = useCan({
		permissions: ['accrediteds_edit']
	})

	const changeVibility = useMutation(async () => {
		return await api.put(`/v1/admin/visibility-accrediteds`, {
			partners: [accredited.secure_id],
			showAccreditedInApp: !accredited.show_accredited_in_app
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['AccreditedsAdmin'])
			toast({
				title: response.data?.message || 'Visibilidade alterada com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao alterar visibilidadedo do credenciado.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const deleteUser = useMutation(async () => {
		return await api.delete(`/v1/admin/accrediteds/${accredited.secure_id}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['AccreditedsAdmin'])
			toast({
				title: response.data?.message || 'Credenciado apagado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao apagar credenciado.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const handleSelect = () => {
		if (checked) {
			setSelectedItems(oldValues => oldValues.filter(product => product !== accredited.secure_id))
			return
		}
		setSelectedItems(oldValues => [...oldValues, accredited.secure_id])
	}

	return (
		<Tr>
			<Td>
				<Checkbox
					isChecked={checked}
					onChange={handleSelect}
				/>
			</Td>
			<Td>
				<Text fontSize="sm">{accredited.name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{accredited.email}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{accredited.legal_document_number}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{accredited?.status ? getUserStatus(accredited.status) : '-'}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{accredited.cell}</Text>
			</Td>
			{(userCanSeeDelete || userCanSeeEdit) && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{userCanSeeEdit && (
								<ButtonToEditItemList
									tooltipLabel="Editar credenciado"
									linkHref={`/admin/accrediteds/${accredited.secure_id}`}
								/>
							)}
							{userCanSeeEdit && (
								<ButtonAlterStatus
									changeStatus={changeVibility}
									titlePopover="Alterar visibilidade no app"
									tooltipLabel="Alterar visibilidade no app"
									message="Deseja Alterar visibilidade no app?"
									status={accredited.show_accredited_in_app}
								/>
							)}
							{userCanSeeDelete && (
								<ButtonDelete
									deleteFunction={deleteUser}
									titlePopover="Remover credenciado"
									tooltipLabel="Remover credenciado"
									message="Deseja remover esse credenciado?"
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
