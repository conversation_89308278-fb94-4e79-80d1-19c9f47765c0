import { Td, Tr, useToast, Text, Flex, <PERSON><PERSON><PERSON>ck, Button, Too<PERSON><PERSON>, Box, useDisclosure, PopoverFooter, <PERSON><PERSON><PERSON>rigger, IconButton } from "@chakra-ui/react"
import { format, parseISO } from "date-fns"
import { FC, useRef, useState } from "react"
import { BsInfoCircle } from "react-icons/bs"
import { useCan } from "~/hooks/useCan"
import { getType } from "~/utils/translateNames/history/appointmentType"
import { RiEyeLine, RiPencilLine } from "react-icons/ri"
import { PopoverComponent } from "~/components/global/Popover"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { SubmitHandler, useForm } from "react-hook-form"
import { getNames } from "~/utils/translateNames/history/status"
import { updateAppointmentStatusFromHistory } from "~/services/history/updateAppointmentStatusFromHistory"
import { updateScheduleStatusFromHistory } from "~/services/history/updateScheduleStatusFromHistory"
import { ModalActionHistory } from "./ModalActionHistory"
import { ObservationModalHistory } from "./ObservationModalHistory"

type FormData = {
	status: string;
	labelStatus: string;
}

type Props = {
	data: {
		type: 'appointment' | 'schedule';
		secureId: string;
		status: string;
		specialtyOrExam: string;
		solicitationDate: string;
		
		appointmentSecureId: string;
		appointmentDate: string;

		partner: {
			secureId: string;
			name: string;
		}
	}
}

export const CardPatientHistory: FC<Props> = ({ data }) => {
	const toast = useToast()

	const { formState, getValues, reset, register, watch } = useForm<FormData>({
		defaultValues: {
			status: data.status,
			labelStatus: getNames(data.type, data.status)
		}
	})

	const statusSelected = watch("status")

	const userCanSeeDelete = useCan({
		permissions: ['patients_delete']
	})

	const userCanSeeEdit = useCan({
		permissions: ['patients_edit']
	})

	const [isOpenPopover, setIsOpenPopover] = useState(false)
	const firstFieldRef = useRef(null)

	const { 
		isOpen: isObservationModalOpen,
		onClose: onObservationModalClosed,
		onToggle: onObservationModalToggle,
		onOpen: onObservationModalOpen
	 } = useDisclosure();
	
	const { 
		isOpen: isHistoricModalOpen,
		onClose: onHistoricModalClosed,
		onToggle: onHistoricModalToggle,
		onOpen: onHistoricModalOpen
	 } = useDisclosure();

	function handleOpenObservationModal() {
		onObservationModalOpen();
	}

	function handleOpenHistoricModal() {
		onHistoricModalOpen();
	}

	function onClosePopover() {
		setIsOpenPopover(false)
		reset()
	}

	function onOpenPopover() {
		setIsOpenPopover(true)
	}

	async function handleChangeStatus(): Promise<void> {
		if (data.type === 'appointment') {
			try {
				const newStatus = getValues('status')
				await updateAppointmentStatusFromHistory(data.appointmentSecureId, newStatus)
				onClosePopover()
				toast({
					title: 'Status do agendamento alterado com sucesso!',
					position: "top-right",
					status: "success",
					isClosable: true,
				})
			} catch(err) {
				toast({
					title: 'Erro ao alterar status do agendamento.',
					position: "top-right",
					status: "error",
					isClosable: true,
				})
			}
		}
		if (data.type === 'schedule') {
			try {
				const newStatus = getValues('status')
				await updateScheduleStatusFromHistory(data.secureId, newStatus)
				onClosePopover()
				toast({
					title: 'Status do agendamento alterado com sucesso!',
					position: "top-right",
					status: "success",
					isClosable: true,
				})
			} catch(err) {
				toast({
					title: 'Erro ao alterar status do agendamento.',
					position: "top-right",
					status: "error",
					isClosable: true,
				})
			}
		}
	}

	return (
		<>
			<Tr id={data.appointmentSecureId}>
				<Td>
					<Text fontSize='sm'>
						{data?.type ? getType(data.type) : '-'}	
					</Text>
				</Td>
				
				<Td>
					<Text fontSize='sm'>
						{data?.appointmentDate ? (
							<>
								{
									format(parseISO(data.appointmentDate), 'dd/MM/yyyy') +
									` ás ` +
									format(parseISO(data.appointmentDate), 'HH:mm')
								}
							</>
						) : '-'}
					</Text>
				</Td>
				
				<Td>
					<Text fontSize='sm'>
						{format(parseISO(data.solicitationDate), 'dd/MM/yyyy')} 
						{' '}ás{' '} 
						{format(parseISO(data.solicitationDate), 'HH:mm')}
					</Text>
				</Td>
				
				<Td>
					<Text fontSize='sm'>{data?.partner?.name ? data?.partner?.name :'-'}</Text>
				</Td>
				
				<Td>
					<Text fontSize='sm'>{data.specialtyOrExam}</Text>
				</Td>
				
				<Td>
					<HStack justify="space-between">
						<Text fontSize='sm'>{data?.status ? getNames(data.type, data.status) : '-'}</Text>
						<PopoverComponent
						isOpen={isOpenPopover}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenPopover}
						onClose={onClosePopover}
						title="Alterar status"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
							>
								{data?.type && data.type === 'appointment' ? (
									<InputSelect
										label="Status"
										placeholder="Selecione um status"
										options={[
											{ label: 'Aprovada', value: 'approved' },
											{ label: 'Não compareceu', value: 'did_not_attend' },
											{ label: 'Realizado', value: 'realized' },
											{ label: 'Finalizado', value: 'finalized' },
											{ label: 'Cancelado pelo Paciente', value: 'canceled_by_Patient' },
											{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
											{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
										]}
										{...register("status")}
									/> 
									) : (
									<InputSelect
										label="Status"
										placeholder="Selecione um status"
										options={[
											{ label: "Cancelado pelo Paciente", value: "canceled_by_patient" },
											{ label: "Cancelado pelo Backoffice", value: "canceled_by_backoffice" },
											{ label: "Cancelado a pedido do Paciente", value: "canceled_at_patient_request" },
											{ label: "Em credenciamento", value: "in_accreditation" },
											{ label: "Sem Contato", value: "no_contact" },
											{ label: "Sem Interesse", value: "no_interest" },
											{ label: "Falta Pedido", value: "lack_request" },
											{ label: "Divergência de Informações", value: "info_divergence" },
											{ label: 'Condição Financeira', value: 'financial_condition' },
											{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
										]}
										{...register("status")}
									/>
								)}
								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={onClosePopover}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit type="button" onClick={handleChangeStatus} isLoading={formState.isSubmitting} isDisabled={!statusSelected}
											>
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />} isDisabled={status === "approved"} />
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
					</HStack>
				</Td>

				{(userCanSeeDelete || userCanSeeEdit) && (
					<Td>
						<Box w="100%" display='flex' flexDir='row' gap={2}>
							<Flex justify="flex-end">
								<HStack>
									<Tooltip label="Visualizar histórico" placement="auto" aria-label="tooltip">
										<Box>
											<Button
												size="sm"
												fontSize="sm"
												cursor="pointer"
												colorScheme="telegram"
												onClick={handleOpenHistoricModal}
											>
												<RiEyeLine size={20} />
											</Button>
										</Box>
									</Tooltip>
								</HStack>
							</Flex>
							
							<Flex justify="flex-end">
								<HStack>
									<Tooltip label="Visualizar observações" placement="auto" aria-label="tooltip">
										<Box>
											<Button
												size="sm"
												fontSize="sm"
												cursor="pointer"
												colorScheme="telegram"
												onClick={handleOpenObservationModal}
											>
												<BsInfoCircle size={20} />
											</Button>
										</Box>
									</Tooltip>
								</HStack>
							</Flex>
						</Box>
					</Td>
				)}
			</Tr>

			<ObservationModalHistory
				isOpen={isObservationModalOpen}
				onClose={onObservationModalClosed}
				onToggle={onObservationModalToggle}
				scheduleSecureId={data.secureId}
				type={data.type}
			/>

			<ModalActionHistory
				isOpen={isHistoricModalOpen}
				closeModal={onHistoricModalClosed}
				chargeSecureId={data.appointmentSecureId ? data.appointmentSecureId : data.secureId}
				type={data.type}
			/>
		</>
	)
}
