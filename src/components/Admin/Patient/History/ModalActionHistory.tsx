import {
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
} from "@chakra-ui/react"
import { FC } from "react"

import { ListActionLog, ListActionLogProps } from "../../ActionLog/ListActionLog"

interface ModalActionAppointmentProps {
	isOpen: boolean
	closeModal: () => void
	
	chargeSecureId: string
	type: ListActionLogProps['type']
}

export const ModalActionHistory: FC<ModalActionAppointmentProps> = ({ closeModal, isOpen, chargeSecureId, type }) => {

	const handleCloseModal = () => {
		closeModal()
	}

	return (
		<Modal size="2xl" isOpen={isOpen} onClose={handleCloseModal}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Histórico</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						width="100%"
						justify="center"
						align="flex-start"
					>
						<ListActionLog
							changedSecureId={chargeSecureId}
							type={type}
						/>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
