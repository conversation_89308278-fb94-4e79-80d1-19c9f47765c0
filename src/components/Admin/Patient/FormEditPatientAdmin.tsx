import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Grid,
	GridItem,
	Divider,
} from "@chakra-ui/react"
import { FC } from "react"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User"
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates"

import { FormUser } from "~/components/global/FormUser"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { Input } from "~/components/global/Form/Input"
import { InputDate } from "~/components/global/Form/InputDate"
import { InputMask } from "~/components/global/Form/InputMask"
import { InputImage } from "~/components/global/Form/ImputImage"

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup
		.string()
		.test('required-min-length', 'Muito curta...', function (value) {
			const isRequired = !!(this.resolve(yup.ref('password')));
			if (!isRequired) {
				return true;
			}
			return value && value.trim().length >= 6 || new yup.ValidationError('Muito curta...', null, this.path);
		}),
	passwordConfirmation: yup
		.string()
		.oneOf([yup.ref("password")], "As senha precisam ser iguais"),
	legal_document_number: yup.string().required("CPF obrigatório"),
	cell: yup.string().required("Celular obrigatório"),
})

interface FormEditPatientAdminProps {
	patient: ShowUser
}

export const FormEditPatientAdmin: FC<FormEditPatientAdminProps> = ({ patient }) => {
	const toast = useToast()

	const {
		register,
		formState: { errors },
		formState,
		handleSubmit,
		watch,
		setValue,
		clearErrors
	} = useForm<UserFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: patient.userInfo.name,
			email: patient.email,
			legal_document_number: patient.userInfo.legal_document_number
				? String(patient.userInfo.legal_document_number.replace(/\D/g, '')) : '',
			cell: `${patient.userInfo.ddd_cell}${patient.userInfo.cell}`,
			zip_code: patient.userInfo.zip_code,
			street: patient.userInfo.street,
			number: patient.userInfo.number,
			complement: patient.userInfo.complement,
			neighborhood: patient.userInfo.neighborhood,
			city: patient.userInfo.city,
			state: patient.userInfo.state,
			birth_date: patient.userInfo.birth_date ? FormatDateForYearMonthDay(patient.userInfo.birth_date) : "",
			avatarSecureId: patient.avatar ? patient.avatar.secure_id : undefined
		},
	})

	const edit = useMutation(
		async (values: UserFormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.put(`/v1/admin/patients/${patient.secure_id}`, {
				...values,
				ddd_cell,
				cell
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PatientsAdmin"])
				toast({
					title:
						response.data?.message || "Paciente alterado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar paciente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<UserFormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch { }
	}

	return (
		<Box as="form" width="100%" onSubmit={handleSubmit(handleEdit)}>
			<VStack spacing="4" align="flex-start">
				<FormUser
					clearErrors={clearErrors}
					formState={formState}
					register={register}
					setValue={setValue}
					watch={watch}
					isEditForm
					textUserExists="isEditForm"
				/>
				<Grid
					templateColumns={{
						sm: 'repeat(4, 1fr)',
						md: 'repeat(8, 1fr)',
						lg: 'repeat(10, 1fr)',
						xl: 'repeat(12, 1fr)',
						'2xl': 'repeat(12, 1fr)',
					}}
					gap={6}
					w="100%"
				>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }} rowSpan={{ sm: 8, lg: 2 }}>
						<InputImage
							name="avatarSecureId"
							label="Avatar"
							watch={watch}
							setValue={setValue}
							clearErrors={clearErrors}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
						<Input
							placeholder="Nome *"
							label="Nome *"
							error={errors.name}
							{...register("name")}
						/>
					</GridItem>

					<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
						<Input
							placeholder="E-mail *"
							label="E-mail *"
							type="email"
							error={errors.email}
							{...register("email")}
						/>
					</GridItem>

					<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }}>
						<InputMask
							label="CPF *"
							placeholder="CPF *"
							mask="999.999.999-99"
							error={errors.legal_document_number}
							{...register('legal_document_number')}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
						<InputMask
							label="Celular *"
							placeholder="Celular *"
							mask="(99)99999-9999"
							error={errors.cell}
							{...register('cell')}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
						<InputDate
							label="Data de nascimento *"
							placeholder="Data de nascimento *"
							{...register('birth_date')}
							error={errors.birth_date}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
						<Divider />
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, '2xl': 2 }}>
						<Input
							placeholder="Cep"
							label="Cep"
							error={errors.zip_code}
							{...register("zip_code")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, '2xl': 8 }} >
						<Input
							placeholder="Endereço"
							label="Endereço"
							error={errors.street}
							{...register("street")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, '2xl': 2 }}>
						<Input
							placeholder="Número"
							label="Número"
							error={errors.number}
							{...register("number")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, '2xl': 4 }}>
						<Input
							placeholder="Complemento"
							label="Complemento"
							error={errors.complement}
							{...register("complement")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 3 }}>
						<Input
							placeholder="Bairro"
							label="Bairro"
							error={errors.neighborhood}
							{...register("neighborhood")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 3 }}>
						<Input
							placeholder="Cidade"
							label="Cidade"
							error={errors.city}
							{...register("city")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 2 }}>
						<Input
							placeholder="Estado"
							label="Estado"
							error={errors.state}
							{...register("state")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
						<Divider />
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
						<InputPassword
							label="Senha *"
							placeholder="Senha *"
							error={errors.password}
							{...register("password")}
						/>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
						<InputPassword
							label="Confirmação de Senha *"
							placeholder="Repetir a Senha *"
							error={errors.passwordConfirmation}
							{...register("passwordConfirmation")}
						/>
					</GridItem>
				</Grid>

				<Flex justify="flex-end" w="100%">
					<HStack spacing="4" width="20em">
						<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
						<ButtonSubmit isLoading={formState.isSubmitting}>
							Salvar
						</ButtonSubmit>
					</HStack>
				</Flex>
			</VStack>
		</Box>
	)
}
