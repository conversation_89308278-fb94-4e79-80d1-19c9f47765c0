import { Td, Text, Tr } from "@chakra-ui/react"
import Link from "next/link"
import { useRouter } from "next/router"

import { FC, useMemo } from "react"

// import ptBR from 'date-fns/locale/pt-BR'
// import { add, differenceInHours, formatDistanceToNowStrict } from "date-fns"

import { ListAppointmentDashboard } from "~/utils/Types/Admin/Dashboard"

interface CardDashboardAppointmentProps {
	appointment: ListAppointmentDashboard
}

export const CardDashboardAppointment: FC<CardDashboardAppointmentProps> = ({ appointment }) => {
	const router = useRouter()

	const backgroundRow = useMemo(() => {
		return appointment.expiration_time.type === 'ok'
			? 'green.100'
			: appointment.expiration_time.type === 'warning'
				? 'yellow.100'
				: 'red.100'
	}, [appointment])

	return (
		<Tr
			bg={backgroundRow}
			cursor="pointer"
			onClick={() => router.push(`admin/schedules/${appointment.secure_id}`)}
		>
			<Td>
				<Text fontSize="sm">{appointment.patient}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{appointment.partner}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{appointment.status}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{appointment.expiration_time.message}</Text>
			</Td>
		</Tr>
	)
}
