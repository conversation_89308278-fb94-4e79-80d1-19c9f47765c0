import {
	Box,
	<PERSON>lex,
	<PERSON>rid,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>rig<PERSON>,
	Td,
	Text,
	Tr,
	useDisclosure,
	useToast,
} from "@chakra-ui/react"
import { FC, useCallback, useEffect, useRef, useState } from "react"

import { RiCalendarCheckLine, RiCalendarLine, RiEyeLine, RiPencilLine } from "react-icons/ri"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { SubmitHandler, useForm } from "react-hook-form"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { PopoverComponent } from "~/components/global/Popover"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { addDays, isWeekend } from "date-fns"
import moment from "moment"
import { FormatDateAsHour } from "~/utils/Functions/FormatDates"
import { getScheduleCurrentStatus } from "~/utils/translateNames/schedule/currentStatus"

type FormData = {
	currentStatus: 'open' | 'closed'
	status: string
	labelStatus: string
	labelCurrentStatus: string
}

type CardScheduleAdminProps = {
	secure_id: string
	patient: string
	type: string
	specialtyOrExam: string
	currentStatus: 'open' | 'closed'
	status: 
		"in_schedule" | "waiting_backoffice_network" | "waiting_backoffice" | "budget" | "waiting_patient" | "approved" | "canceled_by_patient" | "canceled_at_patient_request" | "canceled_by_backoffice" 
		| "in_accreditation" | "no_contact" | "no_interest" | "lack_request" | "info_divergence" | "financial_condition" | "no_interest_accreditation"
	
	followUpDate: string;
	createdAt: string;
	city: string;
}

const getStatus = (status: string) => {
	switch (status) {
		case "in_schedule":
			return "Agenda"

		case "waiting_backoffice":
			return "Aguardando Backoffice Pontual"

		case "waiting_backoffice_network":
			return "Aguardando Backoffice em Rede"

		case "waiting_patient":
			return "Aguardando Paciente"

		case "approved":
			return "Aprovada"

		case "budget":
			return "Orçamento"

		case "canceled_by_patient":
			return "Cancelado pelo Paciente"

		case "canceled_at_patient_request":
			return "Cancelado a pedido do Paciente"

		case "canceled_by_backoffice":
			return "Cancelado pelo Backoffice"

		case "in_accreditation":
			return "Em credenciamento"

		case "no_contact":
			return "Sem Contato"

		case "no_interest":
			return "Sem Interesse"

		case "lack_request":
			return "Falta Pedido"

		case "info_divergence":
			return "Divergência de Informações"

		case "financial_condition":
			return "Condição Financeira"

		case "no_interest_accreditation":
			return "Sem Interesse Credenciamento"

		default:
			return ""
	}
}

export const CardScheduleAdmin: FC<CardScheduleAdminProps> = ({ secure_id, patient, type, specialtyOrExam, currentStatus, status, followUpDate, createdAt, city }) => {
	const toast = useToast()

	const { formState, handleSubmit, reset, register, watch, setValue } = useForm<FormData>({
		defaultValues: {
			status,
			labelStatus: getStatus(status),
			currentStatus,
			labelCurrentStatus: getScheduleCurrentStatus(currentStatus)
		}
	})

	const { 
		isOpen: isCurrentStatusOpen,
		onClose: onCloseCurrentStatus,
		onOpen: onOpenCurrentStatus,
		onToggle: onToggleCurrentStatus
	} = useDisclosure();

	const { 
		isOpen: isOpenPopover,
		onClose: onClosePopover,
		onOpen: onOpenPopover,
		onToggle: onTogglePopover
	} = useDisclosure();

	const statusSelected = watch("status")
	const labelStatus = watch('labelStatus')

	const currentStatusSelected = watch("currentStatus")
	const labelCurrentStatus = watch('labelCurrentStatus')

	const userCanSeeEdit = useCan({
		permissions: ["schedule_edit"]
	})
	const [isManualLoading, setIsManualLoading] = useState<boolean>(false)
	// const [returnDate, setReturnDate] = useState<string>("")

	// const [isOpenPopover, setIsOpenPopover] = useState(false)
	const firstFieldRef = useRef(null)

	const incrementDays = useCallback((parseDateSolicitation: Date, holidays: any) => {
		const threeDaysLater = addDays(parseDateSolicitation, 3)

		let goalDate = threeDaysLater
		let isDateWeekend = isWeekend(goalDate)
		let isDateHoliday = holidays.find((holiday: any) => {
			const formattedReceivedDate = moment(goalDate).format("YYYY-MM-DD")
			const formattedHoliday = moment(holiday.date).format("YYYY-MM-DD")
			return formattedReceivedDate === formattedHoliday
		})
		
		while(isDateWeekend || isDateHoliday) {
			goalDate = addDays(goalDate, 1)
			isDateWeekend = isWeekend(goalDate)
			isDateHoliday = holidays.find((holiday: any) => moment(goalDate).format("YYYY-MM-DD") === moment(holiday.date).format("YYYY-MM-DD"))
		}
		return goalDate
	}, [])
	
	// const generateReturnDate = useCallback(async (dateSolicitation: string) => {
	// 	const holidays = await getHolidaysByDate(dateSolicitation)
	// 	let parseDateSolicitation = parse(dateSolicitation, 'dd/MM/yy \'às\' HH:mm', new Date())
		
	// 	const goalDate = incrementDays(parseDateSolicitation, holidays.dates)

	// 	return goalDate
	// 	}, [dateSolicitation])

	// 	useEffect(() => {
	// 		if (!dateSolicitation) {
	// 			return
	// 		}

	// 		generateReturnDate(dateSolicitation).then((data) => {
	// 			const date = moment(data).format("DD/MM/YY")
	// 			const hours = moment(data).format("HH:mm")
	// 			return setReturnDate(`${date} às ${hours}`)
	// 		})
	// 	}, [dateSolicitation])
			
	// function onClosePopover() {
	// 	setIsOpenPopover(false)
	// 	reset()
	// }

	// function onOpenPopover() {
	// 	setIsOpenPopover(true)
	// }

	const changeStatus = useMutation(async (values: FormData) => {
		return await api.put(`/v1/admin/schedules/${secure_id}`, {
			status: values.status
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(["ScheduleAdmin"])
			toast({
				title: response.data?.message || "Status da solicitação alterada com sucesso!",
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			setValue('status', status)
			toast({
				title: error?.response?.data?.message || "Erro ao alterar status da solicitação.",
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const changeCurrentStatus = useMutation(async (values: FormData) => {
		return await api.put(`/v1/admin/schedules/${secure_id}`, {
			currentStatus: values.currentStatus
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['ScheduleAdmin'])
			toast({
				title: response.data?.message || 'Status atual alterado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			setValue('currentStatus', currentStatus)
			toast({
				title: error?.response?.data?.message || 'Erro ao alterar status atual.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const handleChangeCurrentStatus: SubmitHandler<FormData> = async (values) => {
		try {
			await changeCurrentStatus.mutateAsync(values)
			onCloseCurrentStatus()
		} catch {}
	}

	const handleChangeStatus: SubmitHandler<FormData> = async (values) => {
		try {
			await changeStatus.mutateAsync(values)
			onClosePopover()
		} catch { }
	}

	useEffect(() => {
		setValue('labelStatus', getStatus(statusSelected))
	}, [statusSelected])

	useEffect(() => {
		setValue('labelCurrentStatus', getScheduleCurrentStatus(currentStatusSelected))
	}, [currentStatusSelected])

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{patient}</Text>
			</Td>
			{/* <Td>
				<Text fontSize="sm">{type === "exam" ? "Exame" : type === "in_person" ? "Cons. Presencial" : "Vídeo Chamada"}</Text>
			</Td> */}
			<Td>
				<Text fontSize="sm">{city}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{specialtyOrExam}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{FormatDateAsHour(createdAt)}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{followUpDate ? moment(followUpDate).format('DD/MM/YYYY  HH:mm') : '-'}</Text>
			</Td>

			<Td colSpan={3} bg="white" right='0' position='sticky'>
      <Grid templateColumns="7rem 8rem 4rem" gap='6' alignItems="center" position='relative'>
			{/* <Td position="sticky" right="17rem" bg="white" zIndex="1"> */}
				<HStack justify="space-between" align='center'>
					<Text fontSize="sm">{labelCurrentStatus}</Text>
					<PopoverComponent
						isOpen={isCurrentStatusOpen}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenCurrentStatus}
						onClose={onCloseCurrentStatus}
						title="Alterar status atual"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
								onSubmit={handleSubmit(handleChangeCurrentStatus)}
							>
								<InputSelect
									label="Status atual"
									options={[
										{ label: 'Aberta', value: 'open' },
										{ label: 'Encerrada', value: 'closed' },
									]}
									{...register('currentStatus')}
								/>

								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={() => {
												setValue('currentStatus', currentStatus)
												onCloseCurrentStatus()
											}}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit isLoading={formState.isSubmitting}>
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />}/>
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
				</HStack>
			{/* </Td> */}

			{/* <Td position="sticky" right="6rem" bg="white" zIndex="1"> */}
				<HStack justify="space-between">
				<Text fontSize="sm">{labelStatus}</Text>
					<PopoverComponent
						isOpen={isOpenPopover}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenPopover}
						onClose={onClosePopover}
						title="Alterar sub-status"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
								onSubmit={handleSubmit(handleChangeStatus)}
							>
								<InputSelect
									label="Sub-Status"
									options={[
										{ label: 'Agenda', value: 'in_schedule' },
										{ label: 'Aguardando Backoffice Pontual', value: 'waiting_backoffice' },
										{ label: 'Aguardando Backoffice em Rede', value: 'waiting_backoffice_network' },
										{ label: 'Aguardando Paciente', value: 'waiting_patient' },
										{ label: "Cancelado pelo Paciente", value: "canceled_by_patient" },
										{ label: "Cancelado pelo Backoffice", value: "canceled_by_backoffice" },
										{ label: "Cancelado a pedido do Paciente", value: "canceled_at_patient_request" },
										{ label: "Em credenciamento", value: "in_accreditation" },
										{ label: "Sem Contato", value: "no_contact" },
										{ label: "Sem Interesse", value: "no_interest" },
										{ label: "Falta Pedido", value: "lack_request" },
										{ label: "Divergência de Informações", value: "info_divergence" },
										{ label: 'Condição Financeira', value: 'financial_condition' },
										{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
									]}
									{...register("status")}
								/>

								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={() => {
												setValue('status', status)
												onClosePopover()
											}}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit isLoading={formState.isSubmitting} isDisabled={!statusSelected}>
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />} isDisabled={status === "approved"} />
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
				</HStack>
			{/* </Td> */}
			{userCanSeeEdit && (
				// <Td position="sticky" right="0" bg="white" zIndex="1">
					<Flex justify="flex-end">
						<HStack>
							{["waiting_backoffice_network", "waiting_backoffice", "budget", "in_accreditation"].includes(status) && (
								<ButtonToEditItemList
									icon={RiCalendarLine}
									tooltipLabel="Gerenciar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}`}
								/>
							)}
							{status === "waiting_patient" && (
								<ButtonToEditItemList
									icon={RiCalendarCheckLine}
									tooltipLabel="Aprovar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}/approve`}
								/>
							)}
							{!(["waiting_backoffice", "waiting_backoffice_network", "budget", "waiting_patient", "in_accreditation"].includes(status)) && (
								<ButtonToEditItemList
									icon={RiEyeLine}
									colorScheme="telegram"
									tooltipLabel="Visualizar Solicitação Agendamento"
									linkHref={`/admin/schedules/${secure_id}/view`}
								/>
							)}
						</HStack>
					</Flex>
				// </Td>
			)}
			</Grid>
			</Td>
		</Tr>
	)
}
