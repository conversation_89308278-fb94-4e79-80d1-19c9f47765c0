import {
	<PERSON>,
	Flex,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>overFooter,
	PopoverTrigger,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { RiCalendarLine, RiEyeLine } from "react-icons/ri"

import { useCan } from "~/hooks/useCan"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import moment from "moment"
import { FormatDateAsHour } from "~/utils/Functions/FormatDates"
import { useRouter } from "next/router"

type FormData = {
	status: string
}

type CardScheduleAdminProps = {
	secure_id: string
	patient: string
	type: string
	specialtyOrExam: string
	
	followUpDate: string;
	createdAt: string;
}

export const CardScheduleAdminForDashboard: FC<CardScheduleAdminProps> = ({ secure_id, patient, type, specialtyOrExam, followUpDate, createdAt }) => {
	const toast = useToast()
	const router = useRouter()

	function getBackgroundColor(): string {
		const formattedDate = moment(createdAt).format('YYYY-MM-DD');
	
		const dataRecebida = moment(formattedDate); // Exemplo de data recebida
		const dataAtual = moment(); // Data de hoje
	
		const diferencaDias = dataAtual.diff(dataRecebida, 'days'); // Diferença em dias
	
		if (diferencaDias <= 7) {
			return 'green.100';
		} else if (diferencaDias >= 8 && diferencaDias <= 12) {
			return 'yellow.100';
		} else if (diferencaDias >= 13) {
			return 'red.100';
		} else {
			return '';
		}

	}

	const userCanSeeEdit = useCan({
		permissions: ["schedule_edit"]
	})

	return (
		<Tr
			backgroundColor={getBackgroundColor()}
			borderRadius={12}
			cursor='pointer'
			onClick={() => router.push(`admin/schedules/${secure_id}`)}	
			>
			<Td>
				<Text fontSize="sm">{patient}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{type === "exam" ? "Exame" : type === "in_person" ? "Cons. Presencial" : "Vídeo Chamada"}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{specialtyOrExam}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{FormatDateAsHour(createdAt)}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{followUpDate ? moment(followUpDate).format('DD/MM/YYYY') : '-'}</Text>
			</Td>
			
			{/* {userCanSeeEdit && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{["waiting_backoffice", "budget"].includes(status) && (
								<ButtonToEditItemList
									icon={RiCalendarLine}
									tooltipLabel="Gerenciar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}`}
								/>
							)}
							{status === "waiting_patient" && (
								<ButtonToEditItemList
									icon={RiCalendarLine}
									tooltipLabel="Aprovar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}/approve`}
								/>
							)}
							{!(["waiting_backoffice", "budget", "waiting_patient"].includes(status)) && (
								<ButtonToEditItemList
									icon={RiEyeLine}
									colorScheme="telegram"
									tooltipLabel="Visualizar Solicitação Agendamento"
									linkHref={`/admin/schedules/${secure_id}/view`}
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)} */}
		</Tr>
	)
}
