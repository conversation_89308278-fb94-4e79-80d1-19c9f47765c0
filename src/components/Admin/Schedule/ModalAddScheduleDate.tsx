import {
	Button,
	Grid,
	Grid<PERSON><PERSON>,
	Modal,
	ModalBody,
	<PERSON>dal<PERSON>ontent,
	<PERSON>dal<PERSON><PERSON>er,
	<PERSON>dal<PERSON>eader,
	ModalOverlay,
	Stack,
} from "@chakra-ui/react"
import { FC, useEffect } from "react"

import * as yup from "yup"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"

import { InputDate } from "~/components/global/Form/InputDate"
import { InputSelect } from "~/components/global/Form/InputSelect"

type FormData = {
	date: string
	type: "hour" | "period"
	value: "morning" | "afternoon" | "night" | undefined
}

const FormSchema = yup.object().shape({
	date: yup.string().required("Campo obrigatório *"),
	type: yup.mixed<"hour" | "period">().oneOf(["hour", "period"], "Campo obrigatório *").required("Campo obrigatório *"),
	value: yup.mixed<"morning" | "afternoon" | "night">().oneOf(["morning", "afternoon", "night"]).when("type", {
		is: "period",
		then: schema => schema.required("Campo obrigatório *"),
		otherwise: schema => schema.notRequired()
	}),
})

interface ModalAddScheduleDateProps {
	isOpen: boolean
	enablePeriod: boolean
	onClose: () => void
	handleAddDates: (newDate: FormData & { uniqueKey: string }) => void
}

export const ModalAddScheduleDate: FC<ModalAddScheduleDateProps> = ({ isOpen, onClose, handleAddDates, enablePeriod }) => {
	const { formState: { errors }, handleSubmit, register, resetField, reset, watch } = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			type: 'hour',
		}
	})

	const type = watch("type")

	const handleAdd = async (data: FormData) => {
		try {
			handleAddDates({
				...data,
				uniqueKey: `${data.date}_${Math.random()}`
			})
			onClose()
			reset()
		} catch { }
	}

	useEffect(() => {
		if (type !== "period") {
			resetField("value")
		}
	}, [type])

	return (
		<Modal isOpen={isOpen} onClose={onClose}>
			<ModalOverlay />
			<ModalContent
				as="form"
				onSubmit={handleSubmit(handleAdd)}
			>
				<ModalHeader>Novo Horário</ModalHeader>
				<ModalBody>
					<Stack backgroundColor="white">
						<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
							<GridItem colSpan={12}>
								<InputSelect
									label="Tipo"
									options={[
										{ label: "Hora", value: "hour" },
										{ label: "Período", value: "period" },
									]}
									isDisabled={!enablePeriod}
									{...register("type")}
								/>
							</GridItem>
							<GridItem colSpan={12}>
								<InputDate
									label="Data"
									type="datetime-local"
									{...register("date")}
									error={errors.date}
								/>
							</GridItem>
							{type === "period" && (
								<GridItem colSpan={12}>
									<InputSelect
										label="Período"
										options={[
											{ label: "Manhã", value: "morning" },
											{ label: "Tarde", value: "afternoon" },
											{ label: "Noite", value: "night" },
										]}
										{...register("value")}
									/>
								</GridItem>
							)}
						</Grid>
					</Stack>
				</ModalBody>

				<ModalFooter gap="6">
					<Button variant="ghost" onClick={onClose}>
						Cancelar
					</Button>
					<Button colorScheme="blue" type="submit" mr={3}>
						Adicionar
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	)
}
