import {
	Checkbox,
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"

type CardPartnerAdminProps = {
	partner: ListUserAdmin
}

export const CardPartnerAdmin: FC<CardPartnerAdminProps> = ({ partner }) => {
	const toast = useToast()

	const userCanSeeDelete = useCan({
		permissions: ['partners_delete']
	})

	const userCanSeeEdit = useCan({
		permissions: ['partners_edit']
	})

	const deleteUser = useMutation(async () => {
		return await api.delete(`/v1/admin/partners/${partner.secure_id}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PartnersAdmin'])
			toast({
				title: response.data?.message || 'Parceiro apagado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao apagar parceiro.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{partner.name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{partner.email}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{partner.legal_document_number}</Text>
			</Td>
			{(userCanSeeDelete || userCanSeeEdit) && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{userCanSeeEdit && (
								<ButtonToEditItemList
									tooltipLabel="Editar parceiro"
									linkHref={`/admin/partners/${partner.secure_id}`}
								/>
							)}
							{userCanSeeDelete && (
								<ButtonDelete
									deleteFunction={deleteUser}
									titlePopover="Remover parceiro"
									tooltipLabel="Remover parceiro"
									message="Deseja remover esse parceiro?"
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
