import { Flex, <PERSON><PERSON>tack, Td, Text, Tr, useToast } from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"
import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"

type CardPartnerPatientAdminProps = {
	secureId: string
	partnerId: string
	name: string
}

export const CardPartnerPatientAdmin: FC<CardPartnerPatientAdminProps> = ({
	partnerId,
	secureId,
	name,
}) => {
	const toast = useToast()

	const userCanSeeDelete = useCan({
		permissions: ["partners_delete"],
	})
	
	const userCanEditPatient = useCan({
		permissions: ["partners_edit"],
	})


	const deleteUser = useMutation(
		async () => {
			return await api.delete(`/v1/admin/partners/${partnerId}/patients/${secureId}`)
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PartnerPatientsAdmin"])
				queryClient.invalidateQueries(["ActionLogsAdmin"]);
				toast({
					title: response.data?.message || "Paciente apagado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title: error?.response?.data?.message || "Erro ao apagar paciente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{name}</Text>
			</Td>
			{(userCanSeeDelete) && (
				<Td>
					<Flex justify="center">
						<HStack>
							{userCanSeeDelete && (
								<ButtonDelete
									deleteFunction={deleteUser}
									titlePopover="Remover paciente"
									tooltipLabel="Remover paciente"
									message="Deseja remover esse paciente?"
								/>
							)}
							
							{userCanEditPatient && (
								<ButtonToEditItemList
									tooltipLabel="Editar paciente"
									linkHref={`/admin/patients/${secureId}`}
							/>
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
