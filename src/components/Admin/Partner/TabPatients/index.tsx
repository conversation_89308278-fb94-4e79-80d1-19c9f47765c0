import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Flex,
	HStack,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
	useDisclosure,
	useMediaQuery,
} from "@chakra-ui/react"
import { FC, useMemo } from "react"
import * as yup from 'yup';

import { useCan } from "~/hooks/useCan"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { usePartnerPatientsAdmin } from "~/hooks/Admin/Partners/usePartnerPatientsAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { CardPartnerPatientAdmin } from "./CardPartnerPatientAdmin"
import { ModalImportPartnerPatient } from "./ModalImportPartnerPatient"
import { ButtonToModal } from "~/components/global/Buttons/ButtonToModal"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { FaFilter } from "react-icons/fa"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { CustomBadge } from "~/components/global/Badge/CustomBadge";

interface TabPatientsProps {
	partnerId: string
}

const filterSchema = yup.object().shape({
	status: yup.mixed<'all' | 'active' | 'inactive'>().oneOf(['all', 'active', 'inactive'])
});

export type FilterSchemaType = yup.InferType<typeof filterSchema>;

export const TabPatients: FC<TabPatientsProps> = ({ partnerId }) => {
	const { isOpen, onToggle } = useDisclosure()

	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const filterForm = useForm<FilterSchemaType>(
		{ 
			resolver: yupResolver(filterSchema),
			defaultValues: {
				status: 'all'
			}
		}
	);

	const watchStatus = filterForm.watch('status');

	const { data, isLoading, error, isFetching } = usePartnerPatientsAdmin({
		page,
		search,
		limit,
		partnerId,
		status: watchStatus || 'all'
	})

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const userCanSeeCreate = useCan({
		permissions: ["partners_create"],
	})

	const userCanSeeEdit = useCan({
		permissions: ["partners_edit"],
	})

	const userCanSeeDelete = useCan({
		permissions: ["partners_delete"],
	})

	const dataExportExcel = useMemo(() => {
		if (data) {
			return data.patients.map(patient => ({
				'NOME': patient.userInfo.name,
			}))
		}

		return []
	}, [data])

	function handleResetFilter() {
		filterForm.reset({ status: 'all' });
	}

	function handleResetSearch() {
		setSearch('');
	}

	return (
		<>
			<VStack spacing="4">
				<VStack width="100%" p="4" align="flex-end">
					<HStack spacing="4">
						<ButtonExportExcel
							isDisabled={dataExportExcel.length === 0}
							data={dataExportExcel}
							fileName={`exportar_pacientes`}
						>
							Exportar
						</ButtonExportExcel>
						{userCanSeeCreate && (
							<ButtonToModal
								action={onToggle}
							>
								Importar
							</ButtonToModal>
						)}
					</HStack>
					<Flex w="100%" justify="space-between">
						<Flex>
							{!!error && (
								<Flex justify="center">
									<Text>Falha ao obter dados.</Text>
								</Flex>
							)}
						</Flex>
						<HStack spacing="4" align="center">
							{isFetching && !isLoading && <Spinner />}
							<Box w="72">
								<InputSearch
									name="search"
									placeholder="Nome"
									setPage={setPage}
									setSearch={setSearch}
								/>
							</Box>
						</HStack>
					</Flex>

					<Flex justify="space-between" align="center" w="100%">
						<Accordion w="100%" allowToggle px={0} mx={0}>
							<AccordionItem>
								<AccordionButton px={2}>
									<Box 
										as='span' 
										flex='1' 
										textAlign='left' 
										display='flex' 
										justifyContent='flex-start' 
										alignItems='center'
										gap={4}
									>
										Filtros

										<FaFilter />
									</Box>
									
									<AccordionIcon />
								</AccordionButton>

								<AccordionPanel
									pb={4} 
									display='flex' 
									flexDirection={isTablet ? 'column' : 'row'}
									justifyContent={isTablet ? 'center' : 'space-between'}
									gap={4}
								>
									<InputSelect
										label="Status"
										options={[
											{ label: 'Todos', value: 'all' },
											{ label: 'Ativo', value: 'active' },
											{ label: 'Inativo', value: 'inactive' }
										]}
										{...filterForm.register('status')}
										isDisabled={isLoading || isFetching}
									/> 
								</AccordionPanel>
							</AccordionItem>
						</Accordion>
					</Flex>

					{watchStatus ? (
					<Flex 
							w="100%" 
							align="center" 
							flexWrap='wrap' 
							pb={2}
							mt={2} 
							gap={4}
							borderBottomWidth={1}
							borderBottomColor="gray.200"
						>
							{watchStatus === 'all' ? (
								<CustomBadge title="Status: Todos" />
							) : null}

							{watchStatus === 'active' ? (
								<CustomBadge title="Status: Ativo" handleClose={handleResetFilter} />
							) : null}

							{watchStatus === 'inactive' ? (
								<CustomBadge title="Status: Inativo" handleClose={handleResetFilter} />
							): null}

							{search ? (
								<CustomBadge title={`Busca: ${search}`} handleClose={handleResetSearch} />
							) : null}
							
						</Flex>
				) : null}

					{data && (
						<>
							<TableContainer w="100%">
								<Table>
									<Thead>
										<Tr>
											<Th>Nome</Th>
											{(userCanSeeEdit || userCanSeeDelete) && (
												<Th>
													<Text align="center">Ações</Text>
												</Th>
											)}
										</Tr>
									</Thead>
									<Tbody>
										{data.patients.map((patient) => (
											<CardPartnerPatientAdmin
												key={patient.secure_id}
												partnerId={partnerId}
												name={patient.userInfo?.name}
												secureId={patient.secure_id}
											/>
										))}
									</Tbody>
								</Table>
							</TableContainer>
							<Flex justify="flex-end" w="100%">
								<Pagination
									totalCountOfRegisters={data.total}
									registersInCurrentPage={data.patients.length}
									currentPage={data.page}
									registersPerPage={data.perPage}
									onPageChange={setPage}
									limit={limit}
									setLimit={setLimit}
								/>
							</Flex>
						</>
					)}
				</VStack>
			</VStack>
			{isOpen && (
				<ModalImportPartnerPatient
					isOpen={isOpen}
					closeModal={onToggle}
					partnerId={partnerId}
				/>
			)}
		</>
	)
}
