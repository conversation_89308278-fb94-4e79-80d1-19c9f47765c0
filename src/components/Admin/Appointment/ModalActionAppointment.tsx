import {
  Modal,
  ModalBody,
  ModalClose<PERSON>utton,
  Modal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalOverlay,
  VStack,
} from "@chakra-ui/react";
import { FC } from "react";

import { ListActionLog } from "../ActionLog/ListActionLog";
import { ScheduleDetails } from "./ModalScheduleView";

interface ModalActionAppointmentProps {
  isOpen: boolean;
  closeModal: () => void;
  appointmentSecureId: string;
  scheduleSecureId: string;
}

export const ModalActionAppointment: FC<ModalActionAppointmentProps> = ({
  closeModal,
  isOpen,
  appointmentSecureId,
  scheduleSecureId,
}) => {
  const handleCloseModal = () => {
    closeModal();
  };

  return (
    <Modal size="6xl" isOpen={isOpen} onClose={handleCloseModal}>
      <ModalOverlay />

      <ModalContent bg={"#f2f2f2"} py={4}>
        <ModalCloseButton />
        <ModalBody>
          <VStack>
            <ScheduleDetails
              layerStyle={"card"}
              appointmentSecureId={appointmentSecureId}
              dataSource={"schedule"}
            />
          </VStack>
        </ModalBody>

        <ModalHeader textAlign={"center"}>Histórico De Solicitação</ModalHeader>
        <ModalBody>
          <VStack width={"100%"} justify={"center"} align={"flex-start"} spacing={4}>
            <ListActionLog
              layerStyle={"card"}
              changedSecureId={scheduleSecureId}
              type={"schedule"}
            />
          </VStack>
        </ModalBody>

        <ModalHeader textAlign={"center"}>Histórico De Agendamento</ModalHeader>
        <ModalBody>
          <VStack width="100%" justify="center" align="flex-start" spacing={4}>
            <ListActionLog
              layerStyle="card"
              changedSecureId={appointmentSecureId}
              type={"appointment"}
            />
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
