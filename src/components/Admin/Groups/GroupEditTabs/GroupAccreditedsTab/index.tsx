import {
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
} from "@chakra-ui/react"
import { FC } from "react"

import { useCan } from "~/hooks/useCan"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useGroupAccreditedsAdmin } from "~/hooks/Admin/Groups/useGroupAccreditedsAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { CardGroupAccreditedAdmin } from "./CardGroupAccreditedAdmin"

interface GroupAccreditedsTabProps {
	groupId: string
}

export const GroupAccreditedsTab: FC<GroupAccreditedsTabProps> = ({ groupId }) => {
	const { page, limit, search, setPage, setLimit, setSearch } =
		useControlFilters()

	const { data, isLoading, error, isFetching } = useGroupAccreditedsAdmin({
		page,
		search,
		limit,
		group: groupId,
	})

	const userCanSeeCreate = useCan({
		permissions: ["groups_create"],
	})

	return (
		<VStack spacing="4">
			<VStack width="100%" p="4" align="flex-end">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						{isFetching && !isLoading && <Spinner />}
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
									</Tr>
								</Thead>
								<Tbody>
									{data.accrediteds.map((accredited) => (
										<CardGroupAccreditedAdmin
											key={accredited.secure_id}
											name={accredited.userInfo?.name}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.accrediteds.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}
