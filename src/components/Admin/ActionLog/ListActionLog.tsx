import { BoxProps } from "@chakra-ui/react"
import { FC } from "react"

import { useCan } from "~/hooks/useCan"
import { ListActionLogIntermediate } from "./ListActionLogIntermediate"

export interface ListActionLogProps extends BoxProps {
	changedSecureId: string
	type: 'user' | 'specialty' | 'exam' | 'appointment' | 'schedule' | 'group'
}

export const ListActionLog: FC<ListActionLogProps> = (data) => {
	const userHasPermissionView = useCan({
		permissions: ['action_logs_view']
	})

	if (!userHasPermissionView) {
		return null
	} else {
		return <ListActionLogIntermediate
			{...data}
		/>
	}
}
