import { HStack, Stack, Text } from "@chakra-ui/react"
import { Di<PERSON>atch, SetStateAction } from "react"
import { InputSelect } from "../Form/InputSelect"
import { PaginationItem } from "./PaginationItem"

interface PaginationProps {
	totalCountOfRegisters: number
	registersPerPage: number
	currentPage: number
	registersInCurrentPage?: number
	onPageChange: (page: number) => void
	notTop?: boolean
	limit?: number
	setLimit?: Dispatch<SetStateAction<number>>
	registersPerPageOptions?: number[]
}

const siblingsCount = 1

function generatePagesArray(from: number, to: number) {
	return [...new Array(to - from)]
		.map((_, index) => {
			return from + index + 1
		})
		.filter(page => page > 0)
}

export function Pagination({
	currentPage = 1,
	totalCountOfRegisters,
	onPageChange,
	registersPerPage,
	registersInCurrentPage,
	notTop = false,
	limit,
	setLimit,
	registersPerPageOptions = [25, 50, 75, 100]
}: PaginationProps) {
	const lastPage = Math.ceil(totalCountOfRegisters / registersPerPage)

	const previousPages = currentPage > 1
		? generatePagesArray(currentPage - 1 - siblingsCount, currentPage - 1)
		: []

	const nextPages = currentPage < lastPage
		? generatePagesArray(currentPage, Math.min(currentPage + siblingsCount, lastPage))
		: []

	return (
		<Stack
			w="100%"
			direction="row"
			spacing="6"
			mt="8"
			justify="flex-end"
			align="center"
		>
			{setLimit && limit && (
				<HStack>
					<Text textStyle="textMD">Linhas por pág.</Text>
					<InputSelect
						name="limit"
						value={limit}
						options={registersPerPageOptions.map(register => ({ label: String(register), value: register }))}
						onChange={e => setLimit(Number(e.target.value))}
						variant="unstyled"
					/>
				</HStack>
			)}
			{/* <Box>
				<strong>{(currentPage * registersPerPage) - registersPerPage + 1} </strong>
				-<strong> {(currentPage * registersPerPage) - (registersPerPage - registersInCurrentPage)} </strong>
				de<strong> {totalCountOfRegisters}</strong>
			</Box> */}
			<Stack
				direction="row"
				spacing="2"
			>
				{currentPage > (1 + siblingsCount) && (
					<>
						<PaginationItem onPageChange={onPageChange} number={1} notTop={notTop} />
						{currentPage > (2 + siblingsCount) && (
							<Text
								color="gray.300"
								w="6"
								textAlign="center"
							>
								...
							</Text>
						)}
					</>
				)}

				{previousPages.length > 0 && previousPages.map(page => (
					<PaginationItem onPageChange={onPageChange} key={page} number={page} notTop={notTop} />
				))}

				<PaginationItem onPageChange={onPageChange} number={currentPage} isCurrent notTop={notTop} />

				{nextPages.length > 0 && nextPages.map(page => (
					<PaginationItem onPageChange={onPageChange} key={page} number={page} notTop={notTop} />
				))}

				{(currentPage + siblingsCount) < lastPage && (
					<>
						{(currentPage + 1 + siblingsCount) < lastPage && (
							<Text
								color="gray.300"
								w="6"
								textAlign="center"
							>
								...
							</Text>
						)}
						<PaginationItem onPageChange={onPageChange} number={lastPage} notTop={notTop} />
					</>
				)}
			</Stack>
		</Stack>
	)
}
