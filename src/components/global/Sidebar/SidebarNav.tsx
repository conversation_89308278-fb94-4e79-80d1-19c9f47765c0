import { FC, useMemo } from "react";
import { NavLink } from "./NavLink";
import { NavSection } from "./NavSection";
import {
  RiHome2Line,
  RiStethoscopeLine,
  RiHeartPulseLine,
  RiUser3Line,
  RiGroupLine,
} from "react-icons/ri";
import { ImLab } from "react-icons/im";
import { BsJournalMedical, BsJournalText } from "react-icons/bs";
import { AiOutlineSchedule, AiOutlineCalendar } from "react-icons/ai";
import { TbReport } from "react-icons/tb";
import { useRouter } from "next/router";
import { IconType } from "react-icons";
import { PERMISSIONS } from "~/utils/Types/Permissions";
import { IoNotificationsOutline } from "react-icons/io5";
import { GrScheduleNew } from "react-icons/gr";
import { Submenu } from "./NestedSidebarNav";
interface SidebarNavProps {
  onCloseSidebar?: () => void;
}

interface NavLinksProps {
  id: string;
  icon: IconType;
  href: string;
  permissionLink?: PERMISSIONS;
  title: string;
}

export const SidebarNav: FC<SidebarNavProps> = ({ onCloseSidebar }) => {
  const router = useRouter();
  const currentRoute = router.asPath.split("/")[1];

  const AdminLinks: NavLinksProps[] = [
    {
      id: "1",
      icon: BsJournalMedical,
      href: `/${currentRoute}/accrediteds`,
      permissionLink: "accrediteds_view",
      title: "Credenciados",
    },
    {
      id: "2",
      icon: BsJournalText,
      href: `/${currentRoute}/partners`,
      permissionLink: "partners_view",
      title: "Parceiros",
    },
    {
      id: "3",
      icon: RiHeartPulseLine,
      href: `/${currentRoute}/patients`,
      permissionLink: "patients_view",
      title: "Pacientes",
    },
    {
      id: "4",
      icon: AiOutlineCalendar,
      href: `/${currentRoute}/appointments`,
      permissionLink: "appointment_view",
      title: "Agendamentos",
    },
    {
      id: "5",
      icon: AiOutlineSchedule,
      href: `/${currentRoute}/schedules`,
      permissionLink: "schedule_view",
      title: "Solicitações de Agendamentos",
    },
    {
      id: "6",
      icon: RiUser3Line,
      href: `/${currentRoute}/users`,
      permissionLink: "users_view",
      title: "Usuários",
    },
    {
      id: "7",
      icon: RiGroupLine,
      href: `/${currentRoute}/groups`,
      permissionLink: "groups_view",
      title: "Grupos",
    },
    {
      id: "8",
      icon: RiStethoscopeLine,
      href: `/${currentRoute}/specialties`,
      permissionLink: "specialties_view",
      title: "Especialidades",
    },
    {
      id: "9",
      icon: ImLab,
      href: `/${currentRoute}/exams`,
      permissionLink: "exams_view",
      title: "Exames",
    },
    // {
    //   id: "10",
    //   icon: TbReport,
    //   href: `/${currentRoute}/reports`,
    //   permissionLink: "users_view",
    //   title: "Relatórios",
    // },
    // {
    //   id: "10",
    //   icon: IoNotificationsOutline,
    //   href: `/${currentRoute}/notifications`,
    //   permissionLink: "templatenotification_view",
    //   title: "Notificações",
    // },
  ];

  const DoctorLinks: NavLinksProps[] = [
    {
      id: "3",
      icon: AiOutlineCalendar,
      href: `/${currentRoute}/appointments`,
      title: "Agendamentos",
    },
  ];

  const PartnerLinks: NavLinksProps[] = [
    {
      id: "3",
      icon: RiHeartPulseLine,
      href: `/${currentRoute}/patients`,
      title: "Pacientes",
    },
  ];

  const NavLinks = useMemo(() => {
    if (currentRoute === "admin") {
      return AdminLinks;
    } else if (currentRoute === "doctor") {
      return DoctorLinks;
    } else if (currentRoute === "partner") {
      return PartnerLinks;
    } else {
      return null;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentRoute]);

  return (
    <NavSection>
      <NavLink
        icon={RiHome2Line}
        href={`/${currentRoute}`}
        shouldMatchExactHref={true}
        onCloseSidebar={onCloseSidebar}
      >
        Início
      </NavLink>
      {/* {currentRoute === 'admin' ? (
        <>
          <NavLink
            icon={BsJournalMedical}
            href={`/${currentRoute}/accrediteds`}
            onCloseSidebar={onCloseSidebar}
            permissionLink="accrediteds_view"
          >
            Credenciados
          </NavLink>
        </>
      ) : null}

      {currentRoute === 'doctor' ? (
        <>
          <NavLink
            icon={AiOutlineCalendar}
            href={`/${currentRoute}/appointments`}
            onCloseSidebar={onCloseSidebar}
          >
            Agendamentos
          </NavLink>
        </>
      ) : null} */}

       {NavLinks?.map((nav) => {
         return (
           <NavLink
             key={nav.id}
             icon={nav.icon}
             href={nav.href}
             onCloseSidebar={onCloseSidebar}
             permissionLink={nav.permissionLink}
           >
             {nav.title}
           </NavLink>
         );
       })}

       <Submenu
          icon={TbReport}
          sizeIcon={20}
          text="Relatórios"
          permissionLink="report_appointments_view"
       >
        <NavLink
            icon={GrScheduleNew}
            href={`/${currentRoute}/reports/schedulesAndAppointments`}
            onCloseSidebar={onCloseSidebar}
            permissionLink="report_appointments_view"
          >
            Relatório de Atendimentos
          </NavLink>
       </Submenu>
    </NavSection>
  );
};
