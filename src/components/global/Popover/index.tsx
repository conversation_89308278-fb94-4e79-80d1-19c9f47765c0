import {
	Box,
	<PERSON>over,
	<PERSON>overArrow,
	<PERSON>overBody,
	PopoverCloseButton,
	PopoverContent,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON>overH<PERSON>er,
	PopoverTrigger,
	Tooltip,
	PopoverProps as ChakraPopoverProps,
	Portal
} from "@chakra-ui/react"
import { FC, MutableRefObject, ReactElement } from "react"

interface PopoverProps extends ChakraPopoverProps {
	title: string
	isOpen: boolean
	onClose: () => void
	body: ReactElement
	children: ReactElement
	initialFocusRef: MutableRefObject<any>
	onOpen: () => void
	tooltipLabel?: string
}

export const PopoverComponent: FC<PopoverProps> = ({
	children, body, title, tooltipLabel,
	onOpen, onClose, isOpen, initialFocusRef, ...rest
}) => {
	return (
		<Popover
			isOpen={isOpen}
			initialFocusRef={initialFocusRef}
			onOpen={onOpen}
			onClose={onClose}
			closeOnBlur={false}
			placement="left"
			{...rest}
		>
			{tooltipLabel ? (
				<Tooltip label={tooltipLabel} placement="auto" aria-label="tooltip">
					<Box display="inline-block">
						<PopoverTrigger>
							{children}
						</PopoverTrigger>
					</Box>
				</Tooltip>
			) : (
				<PopoverTrigger>
					{children}
				</PopoverTrigger>
			)}
			<Portal>
				<PopoverContent
					_focus={{ outline: 0 }}
				>
					<PopoverArrow />
					<PopoverCloseButton />
					<PopoverHeader
						pt="6"
						textStyle="headerMD"
						whiteSpace="normal"
					>
						{title}
					</PopoverHeader>
					<PopoverBody>
						{body}
					</PopoverBody>
				</PopoverContent>
			</Portal>
		</Popover>
	)
}
