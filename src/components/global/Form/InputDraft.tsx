import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  useColorMode,
} from "@chakra-ui/react";
import { forwardRef, ForwardRefRenderFunction, lazy, Suspense } from "react";
import { FieldError, UseFormSetValue } from "react-hook-form";
import { Jodit } from "jodit-react";

const Editor = lazy(() => {
  return import("jodit-react");
});

interface InputProps {
  name: string;
  label?: string;
  error?: FieldError;
  initialTemplate?: string;
  setValue: UseFormSetValue<any>;
}

const InputBase: ForwardRefRenderFunction<Jodit, InputProps> = (
  { name, label, error, initialTemplate, setValue, ...rest },
  ref
) => {
  const { colorMode } = useColorMode();
  const isSSR = typeof window === "undefined";

  return (
    <FormControl isInvalid={!!error}>
      {!!label && (
        <FormLabel
          w="100%"
          htmlFor={name}
          display="flex"
          alignItems="center"
          fontWeight="bold"
        >
          {label}
        </FormLabel>
      )}
      {!isSSR && (
        <Suspense fallback={<div>Loading RTE</div>}>
          <Editor
            ref={ref}
            value={initialTemplate || ""}
            config={{
              theme: colorMode,
              height: "20em",
              buttons: [
                "source",
                "|",
                "bold",
                "strikethrough",
                "underline",
                "italic",
                "|",
                "ul",
                "ol",
                "|",
                "outdent",
                "indent",
                "|",
                "font",
                "fontsize",
                "brush",
                "paragraph",
                "|",
                "align",
                "undo",
                "redo",
                "|",
                "hr",
                "fullsize",
              ],
              buttonsXS: [
                "bold",
                "brush",
                "paragraph",
                "|",
                "align",
                "|",
                "undo",
                "redo",
                "|",
                "dots",
              ],
              uploader: {
                insertImageAsBase64URI: true,
              },
            }}
            onChange={(newContent) => setValue(name, newContent)}
          />
          {error && (
            <FormErrorMessage my="0" ml="2" fontSize="xs" paddingTop="2">
              {error.message}
            </FormErrorMessage>
          )}
        </Suspense>
      )}
    </FormControl>
  );
};

export const InputDraft = forwardRef(InputBase);
