import {
	FormControl,
	FormErrorMessage, FormLabel, Input as ChakraInput,
	InputProps as ChakraInputProps, Text
} from '@chakra-ui/react'
import { FieldError } from 'react-hook-form'

import { forwardRef, ForwardRefRenderFunction } from 'react'
import ReactInputMask from 'react-input-mask'

interface InputProps extends ChakraInputProps {
	name: string
	label?: string
	error?: FieldError
	mask?: string
	maskChar?: string
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({ name, label, error = null, ...rest }, ref) => {
	return (
		<FormControl isInvalid={!!error}>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
				>
					{label}
				</FormLabel>
			}
			<ChakraInput
				as={ReactInputMask as any}
				maskChar={null}
				fontSize={{ sm: 'md', md: 'lg' }}
				id={name}
				name={name}
				variant="outline"
				size="lg"
				ref={ref}
				{...rest}
			/>
			{error &&
				<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
			}
		</FormControl>
	)
}

export const InputMask = forwardRef(InputBase)
