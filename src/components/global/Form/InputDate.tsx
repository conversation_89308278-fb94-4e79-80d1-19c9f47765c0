import {
	FormControl,
	FormErrorMessage,
	FormLabel, Input as ChakraInput,
	InputProps as ChakraInputProps,
} from '@chakra-ui/react'
import { FieldError } from 'react-hook-form'

import { forwardRef, ForwardRefRenderFunction } from 'react'
import { format } from 'date-fns'

interface InputProps extends ChakraInputProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
	maxDate?: string
	minDate?: string
	hasMaxDate?: boolean
}

const InputDateBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({ name, label, maxDate, minDate, hasMaxDate = false, error = null, ...rest }, ref) => {
	return (
		<FormControl isInvalid={!!error}>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
				>
					{label}
				</FormLabel>
			}
			<ChakraInput
				fontSize={{ sm: 'md', md: 'lg' }}
				id={name}
				name={name}
				size="lg"
				variant="outline"
				max={
					hasMaxDate ?
						maxDate
							? maxDate
							: format(new Date(), "yyyy-MM-dd")
						: ''
				}
				min={minDate ? minDate : ''}
				type="date"
				ref={ref}
				{...rest}
			/>
			{error &&
				<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
			}
		</FormControl>
	)
}

export const InputDate = forwardRef(InputDateBase)
