import {
	FormControl,
	FormErrorMessage,
	FormLabel,
} from '@chakra-ui/react'

import {
	AsyncSelect as ChakraReactAsyncSelect,
	AsyncProps as ChakraReactSelectAsyncProps, GroupBase
} from "chakra-react-select"

import { FieldError, Controller, Control, Merge, FieldErrorsImpl } from 'react-hook-form'

import { FC } from 'react'
import { Option } from '~/utils/Types/Global'
import { useDebouncedPromise } from '~/utils/Functions/useDebouncedPromise'

interface InputSelectMultProps extends ChakraReactSelectAsyncProps<Option, boolean, GroupBase<Option>> {
	name: string
	label?: string
	placeholder?: string
	error?: Merge<FieldError, FieldErrorsImpl<{
		label: string;
		value: NonNullable<string | number>;
	}>>
	options?: Option[]
	control: Control<any, any>
	handleSearch: (search: string) => Promise<any>
	minSearchChar?: number
  minSearchCharMessage?: string
  noResultsMessage?: string
}

const InputAsyncSelectBase: FC<InputSelectMultProps> = ({
	name,
	label,
	placeholder,
	options = [],
	error = null,
	control,
	handleSearch,
	minSearchChar,
  minSearchCharMessage,
  noResultsMessage,
	...rest }) => {
	const debouncedChange = useDebouncedPromise(handleSearch, 1000)

	const handleNoOptionsMessage = ({ inputValue }: { inputValue: string }) => {
    if (minSearchChar) {
      const cleanedInput = inputValue.replace(/\D/g, '');
      if (cleanedInput.length < minSearchChar) {
        return minSearchCharMessage || `Digite no mínimo ${minSearchChar} dígitos.`;
      }
    }
    return noResultsMessage || "Nenhum resultado encontrado.";
  };

	return (
		<Controller
			control={control}
			name={name}
			render={({
				field: { onChange, onBlur, value, name, ref },
				fieldState: { error }
			}) => (
				<FormControl isInvalid={!!error}>
					{!!label &&
						<FormLabel
							w="100%"
							htmlFor={name}
							display="flex"
							alignItems="center"
							fontWeight="bold"
						>
							{label}
						</FormLabel>
					}

					<ChakraReactAsyncSelect
						isMulti
						size="lg"
						name={name}
						onChange={onChange}
						onBlur={onBlur}
						value={value}
						options={options}
						loadOptions={debouncedChange as (search: string) => Promise<any>}
						placeholder={placeholder}
						// closeMenuOnSelect={false}
						noOptionsMessage={handleNoOptionsMessage}
						loadingMessage={() => "Carregando"}
						ref={ref}
						{...rest}
					/>
					{error &&
						<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
					}
				</FormControl>
			)}
		/>
	)
}

export const InputAsyncSelect = InputAsyncSelectBase
