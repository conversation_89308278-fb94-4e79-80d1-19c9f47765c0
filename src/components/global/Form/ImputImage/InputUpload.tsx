import {
	Box,
	FormControl,
	FormErrorMessage,
	FormLabel,
	HStack,
	Image,
	InputProps as ChakraInputProps,
	Progress,
	Text,
	VStack,
	Icon
} from '@chakra-ui/react'
import { forwardRef, ForwardRefRenderFunction } from 'react'
import Dropzone from "react-dropzone"
import { Control, Controller, FieldError, UseFormWatch } from 'react-hook-form'
import { RiUploadCloudLine } from 'react-icons/ri'

interface InputProps extends ChakraInputProps {
	name: string
	control: Control<any>
	watch: UseFormWatch<any>
	label?: string
	error?: FieldError
	progress?: number
	nameFileActive?: string
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({
	name,
	label,
	error = null,
	progress,
	nameFileActive,
	control,
	watch,
	...rest
}, ref) => {
	const file = watch(name) as File

	return (
		<FormControl
			isInvalid={!!error}
		>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
					textStyle="textMD"
				>
					{label}
					{error &&
						<FormErrorMessage my="0" ml="2" fontSize="xs">{error.message}</FormErrorMessage>
					}
				</FormLabel>
			}
			<Controller
				name={name}
				control={control}
				render={({ field }) => (
					<Dropzone
						multiple={false}
						accept={{
							'image/png': ['.png'],
							'image/jpg': ['.jpg'],
						}}
						onDropAccepted={e => field.onChange(e[0])}
					>
						{({ getRootProps, getInputProps, isDragActive, isDragReject }) => (
							<Box
								border="1px dashed"
								borderColor={error ? "error" : "red.800"}
								rounded="md"
								cursor="pointer"
								height="80"
								background="gray.200"
								transition="all 200ms ease"
								{...getRootProps()}
							>
								<input {...getInputProps()} />
								<HStack
									justify="center"
									align="center"
									h="100%"
									py="3"
									px="2"
								>
									{file && (
										<Image src={URL.createObjectURL(file)} maxH="80" maxW="80" objectFit="contain" />
									)}
									{(!isDragActive && !file) ? (
										<VStack>
											<Icon as={RiUploadCloudLine} fontSize="40" />
											<Text textStyle="textLG" noOfLines={2}>Clique ou arraste</Text>
											<Text textStyle="textMD" noOfLines={2}>JPG ou PNG Máximo 10Mb</Text>
										</VStack>
									) : (isDragReject && !file) ? (
										<Text color="red">Arquivo não suportado</Text>
									) : !file && (
										<Text textStyle="textMD" noOfLines={2}>Solte a imagem aqui</Text>
									)}
								</HStack>
								{!!progress && (
									<Progress
										w="100%"
										colorScheme="green"
										size="sm"
										value={11}
										borderBottomRadius="5"
										mt="-2"
									/>
								)}
							</Box>
						)}
					</Dropzone>
				)}
			/>
		</FormControl>
	)
}

export const InputUpload = forwardRef(InputBase)
