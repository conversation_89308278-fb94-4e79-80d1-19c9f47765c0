import {
	Flex,
	Grid,
	Icon,
	Image,
	Stack
} from "@chakra-ui/react"
import { FC, useState } from "react"

import { UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { IoCheckmarkDoneSharp } from "react-icons/io5"

import { InputSearch } from "../InputSearch"
import { Pagination } from "../../Pagination"
import { ButtonSubmit } from "../../Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "../../Buttons/ButtonCancelSubmit"
import { useUploads } from "~/hooks/Upload/useUploads"

interface TabListSelectImageProps {
	name: string
	watch: UseFormWatch<any>
	setValue: UseFormSetValue<any>
	onClose: () => void
}

export const TabListSelectImage: FC<TabListSelectImageProps> = ({ name, watch, setValue, onClose }) => {
	const [page, setPage] = useState(1)
	const [search, setSearch] = useState<string>()
	const { data } = useUploads({ page, limit: 6, type: 'image', search })

	const fileSelectedSecureId = watch(name)

	return (
		<Stack spacing="4">
			<InputSearch
				name="search"
				placeholder="Pesquise por nome da imagem"
				setPage={setPage}
				setSearch={setSearch}
			/>
			{!!data && (
				<Stack spacing="4">
					<Grid gridTemplateColumns="1fr 1fr 1fr" gap="8">
						{data.uploads?.map(file => (
							<Flex
								as="button"
								key={file.secure_id}
								width="36"
								height="36"
								background="gray.200"
								justify="center"
								align="center"
								rounded="md"
								_focus={{
									background: 'blackAlpha.50',
								}}
								_hover={{
									background: 'blackAlpha.50',
									transition: '200ms'
								}}
								position="relative"
								onClick={() => setValue(name, file.secure_id)}
							>
								{file.secure_id === fileSelectedSecureId && (
									<Icon position="absolute" as={IoCheckmarkDoneSharp} top="2" left="2" color="green" />
								)}
								<Image
									src={file.url}
									objectFit="contain"
									maxW="100%"
									maxH="100%"
								/>
							</Flex>
						))}
					</Grid>
					<Flex justify="flex-end" w="100%">
						<Pagination
							totalCountOfRegisters={data.total}
							registersInCurrentPage={data?.uploads?.length}
							currentPage={data.page}
							registersPerPage={data.perPage}
							onPageChange={setPage}
							notTop
						/>
					</Flex>
				</Stack>
			)}
			<Grid gap="5" gridTemplateColumns="1fr 1fr" w="100%">
				<ButtonCancelSubmit
					onClick={() => {
						setValue(name, undefined)
						onClose()
					}}>
					Cancelar
				</ButtonCancelSubmit>
				<ButtonSubmit
					type="button"
					onClick={onClose}
				>
					Utilizar Imagem Selecionada
				</ButtonSubmit>
			</Grid>
		</Stack>
	)
}
