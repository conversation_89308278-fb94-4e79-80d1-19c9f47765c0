import {
	Grid,
	Stack,
	useToast
} from "@chakra-ui/react"
import { FC, useEffect } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import * as yup from 'yup'
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm, UseFormSetValue } from "react-hook-form"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { Input } from "../Input"
import { InputUpload } from "./InputUpload"
import { ButtonSubmit } from "../../Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "../../Buttons/ButtonCancelSubmit"

type TypeFormData = {
	image: File
	name: string
}

const FormSchema = yup.object().shape({
	image: yup.string().required('Imagem é obrigatória'),
	name: yup.string().required('Nome da imagem é obrigatória'),
})

interface TabUploadImageProps {
	name: string
	setValue: UseFormSetValue<any>
	onClose: () => void
}

export const TabUploadImage: FC<TabUploadImageProps> = ({ setValue, name, onClose }) => {
	const { register, formState, formState: { errors }, watch, handleSubmit, control, setValue: setValueTabUpload, clearErrors } = useForm<TypeFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema)
	})

	const toast = useToast()
	const image = watch('image')

	const uploadImage = useMutation(async (values: TypeFormData) => {
		const formDataAvatar = new FormData()
		formDataAvatar.append('file', image)
		formDataAvatar.append('name', values.name)

		return await api.post('/v1/uploads', formDataAvatar)
	}, {
		onSuccess: (response: AxiosResponse) => {
			setValue(name, response.data.uploadSecureId)
			queryClient.invalidateQueries(['Uploads'])
			toast({
				title: response.data?.message || 'Imagem cadastrada com sucesso!',
				position: "top-right",
				status: "success",
				isClosable: true,
			})
			onClose()
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao cadastrar imagem',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleUpload: SubmitHandler<TypeFormData> = async (values) => {
		try {
			await uploadImage.mutateAsync(values)
		} catch { }
	}

	useEffect(() => {
		setValueTabUpload('name', image?.name.split('.')[0] ?? undefined)
		clearErrors()
	}, [image])

	return (
		<Stack
			spacing="4"
			as="form"
			width="100%"
			onSubmit={handleSubmit(handleUpload)}
		>
			<InputUpload
				watch={watch}
				control={control}
				error={errors.image}
				{...register('image')}
			/>
			<Input
				label="Nome da imagem"
				error={errors.name}
				{...register('name')}
			/>
			<Grid gap="5" gridTemplateColumns="1fr 1fr" w="100%">
				<ButtonCancelSubmit onClick={onClose}>Cancelar</ButtonCancelSubmit>
				<ButtonSubmit
					type="button"
					isLoading={formState.isSubmitting}
					onClick={handleSubmit(handleUpload)}
				>
					Cadastrar e Utilizar Imagem
				</ButtonSubmit>
			</Grid>
		</Stack>
	)
}
