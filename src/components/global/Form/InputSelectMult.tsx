import {
	FormControl,
	FormErrorMessage,
	FormLabel,
} from '@chakra-ui/react'

import {
	Select as ChakraReactSelect,
	Props as ChakraReactSelectProps
} from "chakra-react-select"

import { FieldError, Controller, Control } from 'react-hook-form'

import { FC } from 'react'
import { Option } from '~/utils/Types/Global'

interface InputSelectMultProps extends ChakraReactSelectProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
	options: Option[]
	control: Control<any, any>
}

const InputSelectMultBase: FC<InputSelectMultProps> = ({ name, label, placeholder, options, error = null, control, ...rest }) => {
	return (
		<Controller
			control={control}
			name={name}
			render={({
				field: { onChange, onBlur, value, name, ref },
				fieldState: { error }
			}) => (
				<FormControl py={4} isInvalid={!!error}>
					{!!label &&
						<FormLabel
							w="100%"
							htmlFor={name}
							display="flex"
							alignItems="center"
							fontWeight="bold"
						>
							{label}
						</FormLabel>
					}

					<ChakraReactSelect
						isMulti
						name={name}
						onChange={onChange}
						onBlur={onBlur}
						value={value}
						options={options}
						placeholder={placeholder}
						closeMenuOnSelect={false}
						noOptionsMessage={() => "Sem opções"}
						loadingMessage={() => "Carregando"}
						ref={ref}
						{...rest}
					/>
					{error &&
						<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
					}
				</FormControl>
			)}
		/>
	)
}

export const InputSelectMult = InputSelectMultBase
