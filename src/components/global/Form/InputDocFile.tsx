import {
	Box, FormControl, FormErrorMessage, InputProps as ChakraInputProps, Text, VStack
} from '@chakra-ui/react'
import { forwardRef, ForwardRefRenderFunction } from 'react'
import Dropzone, { Accept } from "react-dropzone"
import { Control, Controller, FieldError, UseFormWatch } from 'react-hook-form'

interface InputProps extends ChakraInputProps {
	name: string
	control: Control<any>
	watch: UseFormWatch<any>
	label?: string
	error?: FieldError
	acceptFiles?: Accept | undefined
}

const InputDocFileBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({
	name,
	label,
	error = null,
	control,
	watch,
	accept,
	acceptFiles = { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx', '.xlsm', '.xltx', '.xltm', '.xlw', '.xlam', '.xlt', '.xlb', '.xlsx', '.xlsb'] },
	...rest
}, ref) => {
	const file = watch(name) as File

	return (
		<FormControl
			isInvalid={!!error}
		>
			<Controller
				name={name}
				control={control}
				render={({ field }) => (
					<Dropzone
						multiple={false}
						accept={acceptFiles}
						onDropAccepted={e => field.onChange(e[0])}
					>
						{({ getRootProps, getInputProps, isDragActive, isDragReject }) => {
							return (
								<Box
									border={"1px dashed"}
									borderColor={error ? "error" : "gray.500"}
									rounded="md"
									cursor="pointer"
									height="40"
									width="100%"
									transition="all 200ms ease"
									{...getRootProps({ className: 'dropzone' })}
								>
									<input {...getInputProps()} />
									<VStack
										justify="center"
										align="center"
										h="100%"
										py="3"
										px="2"
									>
										{file && (
											<Text textStyle="textMD">{file.name}</Text>
										)}
										{(!isDragActive && !file) ? (
											<Text textStyle="textMD" noOfLines={2}>Selecione ou solte um arquivo para ser lido aqui</Text>
										) : (isDragReject && !file) ? (
											<Text color="red">Arquivo não suportado</Text>
										) : !file && (
											<Text textStyle="textMD" noOfLines={2}>Solte o arquivo para ser lido aqui</Text>
										)}
										{error &&
											<FormErrorMessage my="0" ml="2" fontSize="xs">{error.message}</FormErrorMessage>
										}
									</VStack>
								</Box>
							)
						}}
					</Dropzone>
				)
				}
			/>
		</FormControl >
	)
}

export const InputDocFile = forwardRef(InputDocFileBase)
