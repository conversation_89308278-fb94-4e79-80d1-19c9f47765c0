import { Button, ButtonProps } from "@chakra-ui/react"
import { FC, ReactNode } from "react"

interface ButtonCancelSubmitProps extends ButtonProps {
	children: ReactNode
	onClick?: () => void
}

export const ButtonCancelSubmit: FC<ButtonCancelSubmitProps> = ({ children, onClick }) => {
	return (
		<Button
			onClick={() => !!onClick ? onClick() : history.back()}
			size="md"
			width="100%"
		>
			{children}
		</Button>
	)
}
