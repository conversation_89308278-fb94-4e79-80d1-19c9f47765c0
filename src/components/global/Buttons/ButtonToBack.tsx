import { Button, ButtonProps } from "@chakra-ui/react";
import { ReactNode } from "react";
import { AiOutlineArrowLeft } from 'react-icons/ai'

interface ButtonToBackProps extends ButtonProps {
	children: ReactNode;
}

export function ButtonToBack({ children }: ButtonToBackProps) {
	return (
		<Button
			onClick={() => history.back()}
			variant='outline'
			colorScheme="facebook"
			leftIcon={<AiOutlineArrowLeft />}
		>
			{children}
		</Button>
	)
}
