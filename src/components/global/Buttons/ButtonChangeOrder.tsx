import { Button, ButtonProps, Icon } from "@chakra-ui/react"
import { UseMutationResult } from "@tanstack/react-query"
import { RiArrowDownLine, RiArrowUpLine } from "react-icons/ri"
import { AxiosError, AxiosResponse } from "axios"

interface ButtonChangeOrderProps extends ButtonProps {
	order: number
	maxOrder: number
	typeOrder: 'up' | 'down'
	changeOrder: UseMutationResult<AxiosResponse<any, any>, AxiosError<any, any>, number, unknown>
}

export function ButtonChangeOrder({ order, maxOrder, typeOrder, changeOrder, ...rest }: ButtonChangeOrderProps) {
	const handleChangeOrder = async () => {
		try {
			const newOrder = typeOrder === 'up' ? order - 1 : order + 1
			await changeOrder.mutateAsync(newOrder)
		} catch { }
	}

	return (
		<Button
			size="sm"
			fontSize="sm"
			colorScheme="gray"
			isDisabled={(order === 0 && typeOrder === 'up') || (order === maxOrder && typeOrder === 'down')}
			onClick={handleChangeOrder}
			{...rest}
		>
			<Icon as={typeOrder === 'up' ? RiArrowUpLine : RiArrowDownLine} fontSize="20" />
		</Button>
	)
}
