import { Box, Button, ButtonProps, Icon, Tooltip, LightMode } from "@chakra-ui/react"
import Link from "next/link"
import { FC } from "react"
import { IconType } from "react-icons"
import { RiShareBoxLine } from "react-icons/ri"

interface ButtonLinkItemListProps extends ButtonProps {
	tooltipLabel: string
	linkHref: string
	icon?: IconType
	sizeIcon?: string
	target?: "_blank" | "_self" | "_parent" | "_top" | "framename"
}

export const ButtonLinkItemList: FC<ButtonLinkItemListProps> = ({ tooltipLabel, icon, linkHref, sizeIcon, ...rest }) => {
	return (
		<LightMode>
			<Tooltip label={tooltipLabel} placement="auto" aria-label="tooltip">
				<Box display="inline-block">
					<Button
						as={Link}
						href={linkHref}
						passHref
						size="sm"
						fontSize="sm"
						colorScheme="telegram"
						{...rest}
					>
						<Icon as={!!icon ? icon : RiShareBoxLine} fontSize={!!sizeIcon ? sizeIcon : "20"} />
					</Button>
				</Box>
			</Tooltip>
		</LightMode>
	)
}
