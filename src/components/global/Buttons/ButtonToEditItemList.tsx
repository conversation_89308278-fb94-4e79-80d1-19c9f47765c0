import { Box, Button, ButtonProps, Icon, Tooltip, LightMode } from "@chakra-ui/react"
import Link from "next/link"
import { FC } from "react"
import { IconType } from "react-icons"
import { RiPencilLine } from "react-icons/ri"

interface ButtonToEditItemListProps extends ButtonProps {
	tooltipLabel: string
	linkHref: string
	icon?: IconType
	sizeIcon?: string
}

export const ButtonToEditItemList: FC<ButtonToEditItemListProps> = ({ tooltipLabel, icon, linkHref, sizeIcon, ...rest }) => {
	return (
		<LightMode>
			<Tooltip label={tooltipLabel} placement="auto" aria-label="tooltip">
				<Box display="inline-block">
					<Button
						as={Link}
						href={linkHref}
						passHref
						size="sm"
						fontSize="sm"
						colorScheme="telegram"
						{...rest}
					>
						<Icon as={!!icon ? icon : RiPencilLine} fontSize={!!sizeIcon ? sizeIcon : "20"} />
					</Button>
				</Box>
			</Tooltip>
		</LightMode>
	)
}
