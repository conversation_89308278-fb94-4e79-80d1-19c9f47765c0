import { Box, Button, ButtonProps, Icon, Tooltip } from "@chakra-ui/react";
import Link from "next/link";
import { IconType } from "react-icons";
import { RiPencilLine } from "react-icons/ri";

interface ButtonItemListProps extends ButtonProps {
	tooltipLabel: string;
	icon?: IconType;
	sizeIcon?: string;
}

export function ButtonItemList({ tooltipLabel, icon, sizeIcon, ...rest }: ButtonItemListProps) {
	return (
		<Tooltip label={tooltipLabel} placement="auto" aria-label="tooltip">
			<Box display="inline-block">
				<Button
					as="a"
					size="sm"
					fontSize="sm"
					cursor="pointer"
					colorScheme="telegram"
					{...rest}
				>
					<Icon as={!!icon ? icon : RiPencilLine} fontSize={!!sizeIcon ? sizeIcon : "20"} />
				</Button>
			</Box>
		</Tooltip>
	)
}
