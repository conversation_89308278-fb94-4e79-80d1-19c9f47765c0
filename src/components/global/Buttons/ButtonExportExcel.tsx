import {
	Button,
	ButtonProps,
	Icon
} from "@chakra-ui/react"

import { FC, ReactNode, useCallback } from "react"

import { RiFileExcel2Line } from "react-icons/ri"

import { TableExportExcel } from "~/utils/Types/Global"
import { ExportDataToExcel } from "~/utils/Functions/DocFunctions"

interface ButtonExportExcelProps extends ButtonProps {
	children: ReactNode
	data: TableExportExcel[]
	fileName: string
}

export const ButtonExportExcel: FC<ButtonExportExcelProps> = ({ children, data, fileName, ...rest }) => {
	const exportToExcel = useCallback(() => {
		ExportDataToExcel({ data, fileName })
	}, [data])

	return (
		<Button
			as="a"
			size="md"
			fontSize="md"
			colorScheme="whatsapp"
			cursor="pointer"
			leftIcon={<Icon as={RiFileExcel2Line} fontSize="20" />}
			onClick={exportToExcel}
			{...rest}
		>
			{children}
		</Button>
	)
}
