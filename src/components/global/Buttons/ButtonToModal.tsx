import { Button, ButtonProps, Icon } from "@chakra-ui/react"
import { ReactNode } from "react"
import { RiAddLine } from "react-icons/ri"

interface ButtonToModalProps extends ButtonProps {
	children: ReactNode
	action: () => void
	hasIcon?: boolean
}

export function ButtonToModal({ children, action, hasIcon = true, ...rest }: ButtonToModalProps) {
	return (
		<Button
			as="a"
			size="md"
			fontSize="md"
			colorScheme="green"
			leftIcon={hasIcon ? <Icon as={RiAddLine} fontSize="20" /> : undefined}
			onClick={action}
			cursor="pointer"
			{...rest}
		>
			{children}
		</Button>
	)
}