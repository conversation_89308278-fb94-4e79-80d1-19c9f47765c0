import { Badge, Flex } from "@chakra-ui/react";
import { IoClose } from "react-icons/io5";

type Props = {
	title: string;
	handleClose?: () => void;
}

export function CustomBadge({ title, handleClose }: Props) {
	return (
		<Badge 
			pl={4}
			pr={handleClose ? 0 : 4}
			py={1}
			borderRadius={6}
			display='flex'
			flexDir='row'
			justifyContent='space-between'
			alignItems='center'
			as="div"
			gap={3}
			cursor='pointer'
			onClick={handleClose ? handleClose : () => {}}
		>
			{title}
			
			{
				handleClose && (
					<IoClose size={16} style={{ marginRight: '8px'}} />
				)
			}
		</Badge>
	)
}
