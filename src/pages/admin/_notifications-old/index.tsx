import {
	Box,
	Flex,
	HStack,
	SimpleGrid,
	Text,
	VStack,
	useToast
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"
import { AxiosError, AxiosResponse } from "axios"

import * as yup from "yup"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { api } from "~/services/apiClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { Input } from "~/components/global/Form/Input"
import { InputTextarea } from "~/components/global/Form/InputTextarea"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"

type FormData = {
	title: string
	text: string
}

const FormSchema = yup.object().shape({
	title: yup.string().required("Título obrigatório"),
	text: yup.string().required("Texto obrigatório"),
})

interface NotificationsProps {

}

const Notifications: NextPage<NotificationsProps> = () => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		reset
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
		},
	})

	const add = useMutation(
		async (values: FormData) => {
			return await api.post("/v1/admin/notifications", {
				...values,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				reset()
				toast({
					title:
						response.data?.message || "Notificação enviada!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao enviar notificação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch { }
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Notificações
				</Text>
			</Flex>
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
						<Input
							placeholder="Título *"
							label="Título *"
							error={errors.title}
							{...register("title")}
						/>
					</SimpleGrid>
					<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
						<InputTextarea
							placeholder="Texto *"
							label="Texto *"
							error={errors.text}
							{...register("text")}
						/>
					</SimpleGrid>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['notifications_create']
})

export default Notifications
