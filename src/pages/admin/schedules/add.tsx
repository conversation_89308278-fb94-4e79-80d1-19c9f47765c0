import {
  Button,
  Flex,
  Grid,
  HStack,
  Icon,
  IconButton,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useToast,
  VStack,
} from "@chakra-ui/react";
import { GetServerSideProps, NextPage } from "next";
import { Radio, RadioGroup } from "@chakra-ui/react";
import { useCallback, useEffect, useMemo, useState } from "react";

import { IoIosAdd, IoMdTrash } from "react-icons/io";

import * as yup from "yup";
import { AxiosError, AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";
import { yupResolver } from "@hookform/resolvers/yup";
import { SubmitHandler, useForm } from "react-hook-form";

import { api } from "~/services/apiClient";
import { Option } from "~/utils/Types/Global";
import { queryClient } from "~/services/queryClient";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";
import { FormatDateForDayMonthYearUsingBars } from "~/utils/Functions/FormatDates";
import { formatCPF } from "~/utils/Functions/formatCPF";

import { Input } from "~/components/global/Form/Input";
import { InputSelect } from "~/components/global/Form/InputSelect";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { ModalAddScheduleDate } from "~/components/Admin/Schedule/ModalAddScheduleDate";

import type { GetListPatientByCPFResponseDTO } from "~/dto/patients/listByCpf.dto";
import { formatCEP } from "~/utils/Functions/formatCEP";
import { InputTextarea } from "~/components/global/Form/InputTextarea";
import ModalPatientsAdd from "~/components/Admin/Schedule/ModalAddNewPatients";

type ScheduleDate = {
  uniqueKey: string;
  date: string;
  type: "hour" | "period";
  value?: "morning" | "afternoon" | "night";
};

type FormData = {
  typeConsult: "exam" | "in_person" | "video_call" | "laboratory";
  specialtyOrExamSecureId: Option | null;
  status: "waiting_backoffice" | "waiting_backoffice_network" | "budget";
  patientSecureId: Option | null;
  attendantType: "clinic" | "doctor" | "lab" | "helloMed";
  accreditedSecureId: Option | null | undefined;
  local?: {
    zipCode?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
  };
  dates: ScheduleDate[];
};

const patientDependentSchema = yup.object().shape({
  secureId: yup.string().required(),
  name: yup.string().required(),
});

const selectedDependent = yup.object().shape({
  secureId: yup.string().required(),
  name: yup.string().required(),
});

const userType = yup
  .mixed<"patient" | "dependent">()
  .oneOf(["patient", "dependent"], "Campo obrigatório *")
  .required("Campo obrigatório *");

const FormSchema = yup.object().shape({
  typeConsult: yup
    .mixed<"exam" | "in_person" | "video_call" | "laboratory">()
    .oneOf(
      ["exam", "in_person", "video_call", "laboratory"],
      "Campo obrigatório *"
    )
    .required("Campo obrigatório *"),
  status: yup
    .mixed<"waiting_backoffice" | "waiting_backoffice_network" | "budget">()
    .oneOf(
      ["waiting_backoffice", "waiting_backoffice_network", "budget"],
      "Campo obrigatório *"
    )
    .required("Campo obrigatório *"),
  patientSecureId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .nullable()
    .required("Campo obrigatório *"),
  specialtyOrExamSecureId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .nullable()
    .required("Campo obrigatório *"),
  attendantType: yup
    .mixed<"clinic" | "doctor" | "lab" | "helloMed">()
    .oneOf(["clinic", "doctor", "lab", "helloMed"], "Campo obrigatório *")
    .required("Campo obrigatório *"),

  accreditedSecureId: yup
    .object()
    .shape({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .nullable()
    .when("attendantType", {
      is: (value: any) => value !== "helloMed",
      then: (schema) => schema.optional(),
      otherwise: (schema) => schema.notRequired(),
    }),

  local: yup
    .object()
    .shape({
      zipCode: yup.string().optional(),
      neighborhood: yup.string().optional(),
      city: yup.string().optional(),
      state: yup.string().optional(),
    })
    .optional(),
  dates: yup
    .array()
    .optional()
    .of(
      yup.object().shape({
        uniqueKey: yup.string().required(),
        date: yup.string().required(),
        type: yup
          .mixed<"hour" | "period">()
          .oneOf(["hour", "period"], "Campo obrigatório *")
          .required("Campo obrigatório *"),
        value: yup
          .mixed<"morning" | "afternoon" | "night">()
          .oneOf(["morning", "afternoon", "night"])
          .when("type", {
            is: "period",
            then: (schema) => schema.required("Campo obrigatório *"),
            otherwise: (schema) => schema.notRequired(),
          }),
      })
    ),

  userType: userType,

  selectedItemData: yup
    .object()
    .shape({
      patient: yup
        .object()
        .shape({
          secureId: yup.string().required(),
          name: yup.string().required(),
          legalDocument: yup.string().required(),
          email: yup.string().required(),

          partnerName: yup.string().nullable(),
        })
        .nullable(),

      address: yup
        .object()
        .shape({
          zipCode: yup.string().nullable(),
          street: yup.string().nullable(),
          streetNumber: yup.string().nullable(),
          complement: yup.string().nullable(),
          neighborhood: yup.string().nullable().optional().default(""),
          city: yup.string().nullable(),
          state: yup.string().nullable(),
        })
        .nullable(),

      dependents: yup.array(selectedDependent).optional().nullable(),
    })
    .nullable()
    .optional(),

  selectedDependentSecureId: yup.string().optional(),

  observation: yup.string().optional().nullable(),
});

type FormSchemType = yup.InferType<typeof FormSchema>;

interface ScheduleCreateProps {}

const ScheduleCreate: NextPage<ScheduleCreateProps> = () => {
  const toast = useToast();
  const { isOpen, onToggle } = useDisclosure();

  const {
    formState,
    formState: { errors },
    handleSubmit,
    register,
    control,
    watch,
    resetField,
    setValue,
    clearErrors,
    getValues,
    setError,
  } = useForm<FormSchemType>({
    // @ts-ignore
    resolver: yupResolver(FormSchema),
  });

  const watchSelectedItemData = watch("selectedItemData");
  const watchSelectedDependentSecureId = watch("selectedDependentSecureId");

  const dates = watch("dates");
  const local = watch("local");
  const status = watch("status");
  const typeConsult = watch("typeConsult");
  const attendantType = watch("attendantType");
  const specialtyOrExamSecureId = watch("specialtyOrExamSecureId");

  const [accreditedOptions, setAccreditedOptions] = useState<Option[]>([]);
  const [showAddPatientButton, setShowAddPatientButton] = useState(false);
  const [cpfToAdd, setCpfToAdd] = useState<string>("");

  const { isOpen: isOpenAddPatient, onOpen: onOpenAddPatient, onClose: onCloseAddPatient } = useDisclosure();

  const handlePatientCreated = (newPatientData: any) => {
    handleOnChangePatient(newPatientData);

    onCloseAddPatient();

    toast({
      title: "Paciente cadastrado com sucesso!",
      position: "top-right",
      status: "success",
      isClosable: true,
    });
  };

  const attendantTypeOptions = useMemo(() => {
    if (status === "budget") {
      return [
        { label: "Médico", value: "doctor" },
        { label: "Clínica", value: "clinic" },
        { label: "Laboratório", value: "lab" },
      ];
    }

    return [
      { label: "HelloMed", value: "helloMed" },
      { label: "Médico", value: "doctor" },
      { label: "Clínica", value: "clinic" },
      { label: "Laboratório", value: "lab" },
    ];
  }, [status]);

  const isDisabledSearchAccredited = useMemo(() => {
    return !(
      attendantType !== "helloMed" &&
      specialtyOrExamSecureId?.value &&
      local?.state?.trim() &&
      local?.city?.trim()
    );
  }, [attendantType, specialtyOrExamSecureId, local?.state, local?.city]);

  const isDisabledChangeStatus = useMemo(() => {
    if (!dates) {
      return false;
    }
    return dates.some((obj) => obj.type === "period");
  }, [dates]);

  const handleSearchPatients = async (search: string) => {
    const cleanedSearch = search.replace(/\D/g, "");

    if (cleanedSearch.length < 6) {
      setShowAddPatientButton(false);
      return Promise.resolve([]);
    }

    setCpfToAdd(cleanedSearch); 

    try {
      const userType = getValues("userType") || "patient";

      const response = await api.get<GetListPatientByCPFResponseDTO[]>(
        `v1/admin/list/patients-by-cpf/${userType}/${cleanedSearch}`
      );

      const patientData = response.data.map((data) => {
        const label = `${data.patientData.name} - (${data.patientData.legalDocument})`;

        return {
          label: label,
          value: data.patientData.secureId,
          patientData: data.patientData,
          addressData: data.addressData,
          dependentsData: data.dependentsData,
        };
      });

      if (patientData.length === 0) {
        setShowAddPatientButton(true);
      } else {
        setShowAddPatientButton(false);
      }

      return patientData;
    } catch (error) {
      setShowAddPatientButton(true);
      console.error("Erro ao buscar pacientes:", error);
      return [];
    }
  };

  const handleSearchExams = async (search: string) => {
    const { data } = await api.get("v1/admin/list/exams", {
      params: {
        search,
      },
    });

    const exams = data.map((exam: any) => ({
      label: exam.name,
      value: exam.secure_id,
    }));

    return exams;
  };

  const handleSearchSpecialties = async (search: string) => {
    const { data } = await api.get("v1/admin/list/specialties", {
      params: {
        search,
      },
    });

    const specialties = data.map((specialty: any) => ({
      label: specialty.name,
      value: specialty.secure_id,
    }));

    return specialties;
  };

  const handleSearchAccrediteds = useCallback(
    async (search?: string) => {
      const { data } = await api.get("v1/public/attendants", {
        params: {
          search,
          limit: 999,
          local,
          typeConsult,
          attendantType,
          specialtyOrExamSecureId: specialtyOrExamSecureId?.value,
        },
      });

      const accrediteds = data.data.map((user: any) => ({
        label: user.userInfo.name,
        value: user.secure_id,
      }));

      return accrediteds;
    },
    [
      attendantType,
      specialtyOrExamSecureId,
      local?.zipCode,
      local?.neighborhood,
    ]
  );

  const handleGetDefaultAccredited = async (): Promise<void> =>
    handleSearchAccrediteds().then((data) => setAccreditedOptions(data));

  const handleAddDates = useCallback(
    (newDate: ScheduleDate) => {
      const newDates = dates ? [...dates, newDate] : [newDate];
      setValue("dates", newDates);

      clearErrors("dates");
    },
    [setValue, clearErrors, dates]
  );

  const handleRemoveDate = useCallback(
    (dateRemove: ScheduleDate) => {
      if (dates) {
        setValue(
          "dates",
          dates.filter((date) => date.uniqueKey !== dateRemove.uniqueKey)
        );
      } else {
        setValue("dates", []);
      }
    },
    [setValue, dates]
  );

  const getCep = async (value: string) => {
    if (value && value.length === 8) {
      api
        .get(`https://brasilapi.com.br/api/cep/v2/${value}`)
        .then((response) => {
          const data: any = response.data;
          setValue("local.city", data.city);
          setValue("local.neighborhood", data.neighborhood);
          setValue("local.state", data.state);
        })
        .catch(() => {
          // setBairro('')
          // setCity('')
        });
    }
  };

  useEffect(() => {
    if (local && local.zipCode) {
      getCep(local.zipCode.replace(/[^\d]/g, ""));
    }
  }, [local?.zipCode]);

  const add = useMutation(
    async (values: FormSchemType) => {
      return await api.post("/v1/admin/schedules", {
        ...values,
        patientSecureId: values?.selectedDependentSecureId
          ? values?.selectedDependentSecureId
          : values.patientSecureId?.value,
        specialtyOrExamSecureId: values.specialtyOrExamSecureId?.value,
        accreditedSecureId: values.accreditedSecureId?.value,
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["ScheduleAdmin"]);
        toast({
          title:
            response.data?.message ||
            "Nova solicitação de agendamento cadastrada com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
        history.back();
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Ocorreu um problema ao cadastrar solicitação de agendamento.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleAdd: SubmitHandler<FormSchemType> = async (values) => {
    try {
      if (
        values.userType === "dependent" &&
        !values.selectedDependentSecureId
      ) {
        toast({
          title: "Selecione um dependente para continuar.",
          position: "top-right",
          status: "error",
          isClosable: true,
        });

        setError("selectedDependentSecureId", {
          message: "Campo obrigatório *",
        });

        return;
      }

      await add.mutateAsync(values);
    } catch {}
  };

  useEffect(() => {
    resetField("specialtyOrExamSecureId", {
      // @ts-ignore
      defaultValue: null,
    });
  }, [typeConsult]);

  useEffect(() => {
    if (attendantType === "helloMed") {
      resetField("accreditedSecureId", {
        defaultValue: null,
      });
    }
  }, [attendantType]);

  useEffect(() => {
    if (status === "budget" && attendantType === "helloMed") {
      setValue("attendantType", "doctor");
    }
  }, [status]);

  useEffect(() => {
    if (!isDisabledSearchAccredited) {
      handleGetDefaultAccredited();
    }
  }, [
    attendantType,
    specialtyOrExamSecureId,
    local?.neighborhood,
    local?.city,
    local?.state,
  ]);

  function handleOnChangePatient(data: GetListPatientByCPFResponseDTO): void {
    if (!data) {
      setValue("selectedItemData", null);
      setValue("selectedDependentSecureId", undefined);
      setShowAddPatientButton(false);
      setCpfToAdd("");
      return;
    }

    setShowAddPatientButton(false);
    setCpfToAdd("");

    setValue("patientSecureId", {
      value: data.patientData.secureId,
      label: `${data.patientData.name} - (${data.patientData.legalDocument})`,
    });
    
    setValue("selectedItemData.patient.secureId", data.patientData.secureId);
    setValue("selectedItemData.patient.name", data.patientData.name);
    setValue("selectedItemData.patient.email", data.patientData.email);
    setValue(
      "selectedItemData.patient.legalDocument",
      data.patientData.legalDocument
    );
    setValue(
      "selectedItemData.patient.partnerName",
      data.patientData.partnerName
    );

    setValue("selectedItemData.address.zipCode", data.addressData.zipCode);
    setValue("selectedItemData.address.street", data.addressData.street);
    setValue(
      "selectedItemData.address.streetNumber",
      data.addressData.streetNumber
    );
    setValue(
      "selectedItemData.address.complement",
      data.addressData.complement
    );
    setValue(
      "selectedItemData.address.neighborhood",
      data.addressData.neighborhood ?? null
    );
    setValue("selectedItemData.address.city", data.addressData.city);
    setValue("selectedItemData.address.state", data.addressData.state);

    setValue(
      "selectedItemData.dependents",
      data?.dependentsData ? data?.dependentsData : undefined
    );
  }

  function handleChangeUserType(): void {
    setValue("selectedItemData", null);
    resetField("patientSecureId", {
      // @ts-ignore
      defaultValue: null,
    });
  }

  function handleChangeSelectedDependent(value: any) {
    setValue("selectedDependentSecureId", value);
  }

  function onError(data: any) {
    console.log(data);
  }

  const isExamOrLaboratory = (type: string) =>
    type === "exam" || type === "laboratory";

  const watchUserType = watch("userType");

  return (
    <>
      <VStack
        spacing="4"
        layerStyle="container"
        align="flex-start"
        as="form"
        onSubmit={handleSubmit(handleAdd, onError)}
      >
        <VStack w="100%" p="4" layerStyle="card">
          <Grid
            w="100%"
            templateColumns={{
              sm: "1fr",
              lg: "0.5fr minmax(180px, 1fr) 0.5fr",
            }}
            gap="6"
          >
            <InputSelect
              label="Titular ou Dependente"
              options={[
                { label: "Titular", value: "patient" },
                { label: "Dependente", value: "dependent" },
              ]}
              // isDisabled={isDisabledChangeStatus}
              {...register("userType")}
              onChange={handleChangeUserType}
            />

            <HStack align={"flex-end"}>
              <InputAsyncSelect
                isMulti={false}
                isClearable
                control={control}
                name="patientSecureId"
                error={errors.patientSecureId}
                label="Paciente"
                placeholder="Procure pelo cpf do titular"
                handleSearch={handleSearchPatients}
                onChange={(e) => {
                  handleOnChangePatient(e as never);
                }}
                minSearchChar={6}
                minSearchCharMessage="Digite no mínimo 6 dígitos do CPF"
                noResultsMessage={watchUserType === "patient" ? "Paciente Novo" : "Nenhum paciente encontrado" }
              />

              {showAddPatientButton && watchUserType === "patient" && (
                <IconButton
                  aria-label={"Adicionar novo Paciente"}
                  size={"lg"}
                  w={"50px"}
                  colorScheme={"green"}
                  onClick={onOpenAddPatient}
                >
                  <IoIosAdd size={"25px"} color="white" />
                </IconButton>
              )}
            </HStack>

            <InputSelect
              label="Status"
              options={[
                {
                  label: "Aguardando Backoffice Pontual",
                  value: "waiting_backoffice",
                },
                {
                  label: "Aguardando backoffice em Rede",
                  value: "waiting_backoffice_network",
                },
                { label: "Orçamento", value: "budget" },
              ]}
              isDisabled={isDisabledChangeStatus}
              {...register("status")}
            />
          </Grid>

          {watchSelectedItemData ? (
            <Grid
              w="100%"
              templateColumns={{ lg: "repeat(2, minmax(180px, 1fr))" }}
              gap="6"
              my={2}
              px={2}
            >
              {/* GRID: Usuário */}
              <Flex flexDirection="column" w="100%">
                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Nome:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.patient?.name
                      ? watchSelectedItemData?.patient?.name
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    CPF:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.patient?.legalDocument
                      ? formatCPF(watchSelectedItemData?.patient?.legalDocument)
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Parceiro:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.patient?.partnerName
                      ? watchSelectedItemData?.patient?.partnerName
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    E-mail:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.patient?.email
                      ? watchSelectedItemData?.patient?.email
                      : "-"}
                  </Text>
                </HStack>

                {watchSelectedItemData?.dependents &&
                watchSelectedItemData?.dependents.length > 0 ? (
                  <>
                    <RadioGroup
                      onChange={handleChangeSelectedDependent}
                      value={watchSelectedDependentSecureId}
                    >
                      {/* <RadioGroup onChange={setValue} value={value}> */}

                      {watchSelectedItemData?.dependents.map(
                        (dependent, index) => (
                          <Radio
                            value={dependent.secureId}
                            key={dependent.secureId}
                          >
                            <HStack
                              my={2}
                              flexWrap="wrap"
                              key={dependent.secureId}
                            >
                              <Text
                                fontSize={{ sm: "md", md: "lg" }}
                                fontWeight="bold"
                              >
                                Dependente:
                              </Text>

                              <Text fontSize={{ sm: "md", md: "lg" }}>
                                {dependent?.name ? dependent?.name : "-"}
                              </Text>
                            </HStack>
                          </Radio>
                        )
                      )}
                    </RadioGroup>

                    {errors.selectedDependentSecureId ? (
                      <Text color="red">
                        {errors?.selectedDependentSecureId?.message}
                      </Text>
                    ) : null}
                  </>
                ) : null}
              </Flex>

              {/* GRID: Endereço */}
              <Flex flexDirection="column" w="100%" alignSelf="flex-start">
                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    CEP:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.zipCode
                      ? formatCEP(watchSelectedItemData?.address?.zipCode)
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Endereço:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.street
                      ? watchSelectedItemData?.address?.street
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Número:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.streetNumber
                      ? watchSelectedItemData?.address?.streetNumber
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Complemento:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.complement
                      ? watchSelectedItemData?.address?.complement
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Bairro:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.neighborhood
                      ? watchSelectedItemData?.address?.neighborhood
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Cidade:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.city
                      ? watchSelectedItemData?.address?.city
                      : "-"}
                  </Text>
                </HStack>

                <HStack my={2} flexWrap="wrap">
                  <Text fontSize={{ sm: "md", md: "lg" }} fontWeight="bold">
                    Estado:
                  </Text>

                  <Text fontSize={{ sm: "md", md: "lg" }}>
                    {watchSelectedItemData?.address?.state
                      ? watchSelectedItemData?.address?.state
                      : "-"}
                  </Text>
                </HStack>
              </Flex>
            </Grid>
          ) : null}
        </VStack>

        <VStack w="100%" p="4" layerStyle="card">
          <Grid
            w="100%"
            templateColumns={{ sm: "1fr", lg: "0.3fr minmax(180px, 1fr)" }}
            gap="6"
          >
            <InputSelect
              label="Tipo da consulta"
              options={[
                { label: "Clínica", value: "in_person" },
                { label: "Vídeo Chamada", value: "video_call" },
                { label: "Exame", value: "exam" },
                { label: "Laboratório", value: "laboratory" },
              ]}
              {...register("typeConsult")}
            />

            <InputAsyncSelect
              isMulti={false}
              isClearable
              control={control}
              name="specialtyOrExamSecureId"
              error={errors.specialtyOrExamSecureId}
              label={
                isExamOrLaboratory(typeConsult) ? "Exame" : "Especialidade"
              }
              placeholder={
                isExamOrLaboratory(typeConsult) 
                  ? "Procure um exame"
                  : "Procure uma especialidade"
              }
              handleSearch={
                isExamOrLaboratory(typeConsult)
                  ? handleSearchExams
                  : handleSearchSpecialties
              }
            />
          </Grid>

          <Grid
            w="100%"
            templateColumns={{ sm: "1fr", lg: "repeat(4, 1fr)" }}
            gap="6"
          >
            <Input
              placeholder="Cep"
              label="Cep"
              error={errors.local?.zipCode}
              {...register("local.zipCode")}
            />
            <Input
              label="Bairro"
              placeholder="Digite o bairro"
              error={errors.local?.neighborhood}
              {...register("local.neighborhood")}
            />
            <Input
              label="Cidade"
              placeholder="Digite a cidade"
              error={errors.local?.city}
              {...register("local.city")}
            />
            <Input
              label="Estado"
              placeholder="Digite o estado"
              error={errors.local?.state}
              {...register("local.state")}
            />
          </Grid>

          <Grid
            w="100%"
            templateColumns={{ sm: "1fr", lg: "0.3fr minmax(180px, 1fr)" }}
            gap="6"
          >
            <InputSelect
              label="Tipo de atendimento"
              options={attendantTypeOptions}
              {...register("attendantType")}
            />
            <InputAsyncSelect
              isMulti={false}
              isClearable
              control={control}
              label="Credenciado"
              name="accreditedSecureId"
              placeholder="Procure um credenciado"
              error={errors.accreditedSecureId}
              handleSearch={handleSearchAccrediteds}
              isDisabled={isDisabledSearchAccredited}
              defaultOptions={accreditedOptions}
            />
          </Grid>

          <Input
            label="Observação"
            error={errors.observation}
            {...register("observation")}
          />
        </VStack>

        <VStack align="flex-start">
          <Button colorScheme="blue" onClick={onToggle}>
            Adicionar Horário
          </Button>
          {errors.dates && (
            <Text as="span" textStyle="textSM" fontSize="xs" color="red.500">
              {errors.dates.message}
            </Text>
          )}
        </VStack>

        <VStack w="100%" p="4" layerStyle="card">
          <TableContainer w="100%">
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Data</Th>
                  <Th>Tipo</Th>
                  <Th>Período</Th>
                  <Th>Ações</Th>
                </Tr>
              </Thead>
              <Tbody>
                {dates &&
                  dates.map((item, index) => (
                    <Tr key={item.uniqueKey}>
                      <Td>{FormatDateForDayMonthYearUsingBars(item.date)}</Td>
                      <Td>{item.type === "hour" ? "Hora" : "Período"}</Td>
                      <Td>
                        {item.value === "morning"
                          ? "Manhã"
                          : item.value === "afternoon"
                          ? "Tarde"
                          : item.value === "night"
                          ? "Noite"
                          : ""}
                      </Td>
                      <Td>
                        <IconButton
                          variant="ghost"
                          colorScheme="red"
                          aria-label="Remove Date"
                          fontSize="20"
                          icon={<Icon as={IoMdTrash} />}
                          onClick={() => handleRemoveDate(item)}
                        />
                      </Td>
                    </Tr>
                  ))}
              </Tbody>
            </Table>
          </TableContainer>
        </VStack>

        <Flex justify="flex-end" w="100%">
          <HStack spacing="4" width="20em">
            <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
            <ButtonSubmit isLoading={formState.isSubmitting}>
              Cadastrar
            </ButtonSubmit>
          </HStack>
        </Flex>
      </VStack>

      {isOpen && (
        <ModalAddScheduleDate
          isOpen={isOpen}
          onClose={onToggle}
          handleAddDates={handleAddDates}
          enablePeriod={status === "waiting_backoffice"}
        />
      )}

      {isOpenAddPatient && (
        <ModalPatientsAdd 
          isOpen={isOpenAddPatient}
          onClose={onCloseAddPatient}
          onPatientCreated={handlePatientCreated}
          initialCpf={cpfToAdd}
        />
      )}
    </>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    return {
      props: {},
    };
  },
  {
    roles: ["MASTER", "ADMIN"],
    permissions: ["schedule_create"],
  }
);

export default ScheduleCreate;
