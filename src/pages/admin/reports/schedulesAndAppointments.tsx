import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Button,
	Flex,
	Grid,
	HStack,
	Icon,
	Progress,
	SimpleGrid,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	useMediaQuery,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useScheduleAdmin } from "~/hooks/Admin/Schedule/useScheduleAdmin"
import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"


import { CardScheduleAdmin } from "~/components/Admin/Schedule/CardScheduleAdmin"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { Pagination } from "~/components/global/Pagination"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSortTable } from "~/components/global/Buttons/ButtonSortTable"
import { FormatDateAsHour } from "~/utils/Functions/FormatDates"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { InputDate } from "~/components/global/Form/InputDate"
import { useSchedulesAndAppointmentsReport } from "~/hooks/Admin/Reports/SchedulesAndAppointments"
import { CardScheduleAndAppointments } from "~/components/Admin/Reports/CardScheduleAndAppointments"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { useCallback, useEffect, useMemo, useState } from "react"
import { masks } from "~/utils/Functions/Masks"
import { getStatus } from "~/utils/Functions/AppointmentAndScheduleStatus"
import { formatCurrency } from "~/utils/Functions/formatCurrency"
import { ButtonExportPDF } from "~/components/global/Buttons/ButtonExportPDF"
import { FaFilter } from "react-icons/fa"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import * as yup from 'yup';
import { queryClient } from "~/services/queryClient"
import { CustomBadge } from "~/components/Admin/Appointment/CustomBadge"
import { InputSelectMult } from "~/components/global/Form/InputSelectMult"
import { useSchedulesAndAppointmentsGetCitiesByUFS } from "~/hooks/Admin/Reports/useSchedulesAndAppointmentsGetCitiesByUFS"
import { useSchedulesAndAppointmentsDependencies } from "~/hooks/Admin/Reports/useSchedulesAndAppointmentsDependencies"
import { getAppointmentAndScheduleExport } from "~/services/reports/getAppointmentAndScheduleExport"
import { RiFileExcel2Line } from "react-icons/ri"
import { ExportDataToExcel, ExportDataToPDF } from "~/utils/Functions/DocFunctions"
import { PiFilePdfLight } from "react-icons/pi"

interface ScheduleProps {

}

const filterSchema = yup.object().shape({
	specialtyOrExam: yup.array().of(
		yup.object().shape({
			label: yup.string(),
			value: yup.string(),
		})
	).optional(),
	uf: yup.array().of(
		yup.object().shape({
			label: yup.string(),
			value: yup.string(),
		})
	).optional(),
	city: yup.array().of(
		yup.object().shape({
			label: yup.string(),
			value: yup.string(),
		})
	).optional(),
	appointmentStatus: yup.array().of(
		yup.object().shape({
			label: yup.string(),
			value: yup.string(),
		})
	).optional(),
	scheduleStatus: yup.array().of(
		yup.object().shape({
			label: yup.string(),
			value: yup.string(),
		})
	).optional(),
	openingDate: yup.string().optional(),
	scheduleOrAppointment: yup.string().optional(),
})

type FilterSchemaType = yup.InferType<typeof filterSchema>

const ScheduleReports: NextPage<ScheduleProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch, direction, field, setField, setDirection, fieldSelect, setFieldSelect } = useControlFilters()

	const [isExporting, setIsExporting] = useState(false)

	const {formState, control, watch, register, setValue} = useForm<FilterSchemaType>(
		{ 
			resolver: yupResolver(filterSchema),
			defaultValues: {
				openingDate: '',
				scheduleOrAppointment: 'both'
			}
		}
	);

	// const filterForm = useForm<FilterSchemaType>(
	// 	{ 
	// 		resolver: yupResolver(filterSchema),
	// 		defaultValues: {
	// 			uf: 'Selecione um estado',
	// 			specialtyOrExam: 'Selecione uma especialidade/exame',
	// 			city: 'Selecione uma cidade',
	// 		}
	// 	}
	// );

	const watchUf = watch('uf');
	const watchSpecialtyOrExam = watch('specialtyOrExam');
	const watchCity = watch('city')
	const watchOpeningDate = watch('openingDate')
	const watchScheduleOrAppointment = watch('scheduleOrAppointment')

	const watchAppointmentStatus = watch('appointmentStatus')
	const watchScheduleStatus = watch('scheduleStatus')

	const appointmentStatusValues = watchAppointmentStatus?.map(item => item.value) || [];
	const scheduleStatusValues = watchScheduleStatus?.map(item => item.value) || [];
	
	const specialtyOrExamValues = watchSpecialtyOrExam?.map(item => item.value) || [];
	const UfValues = watchUf?.map(item => item.value) || [];
	const citiesValues = watchCity?.map(item => item.value) || [];

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const generateExportData = useCallback(async () => {
		const exportDataReport = await getAppointmentAndScheduleExport({
			page,
			limit,
			targeting: fieldSelect,
	
			stateUFS: UfValues,
			citiesNames: citiesValues,
			specialtySecureId: specialtyOrExamValues,
			examSecureId: specialtyOrExamValues,
			openingDate: watchOpeningDate,
			scheduleOrAppointment: watchScheduleOrAppointment,
			appointmentStatus: appointmentStatusValues,
			scheduleStatus: scheduleStatusValues
		});
	
		if (exportDataReport && exportDataReport?.data?.length > 0) {
			const mappedData = exportDataReport.data.map(data => {
				const isSchedule = !data.appointment!!;
				const specialtyOrExam = data.specialty ? data.specialty.name : data.exam?.name;
				const formattedPhone = masks('cellPhone', `${data.patient.userInfo.ddd_cell}${data.patient.userInfo.cell}`);
	
				return {
					'DATA DE ABERTURA': FormatDateAsHour(data.created_at),
					'CPF DO TITULAR': data.patient.userInfo.legal_document_number ? data.patient.userInfo.legal_document_number : '',
					'NOME DO PACIENTE': data.patient.userInfo.name,
					'TELEFONE DO PACIENTE': formattedPhone,
					'CIDADE DO PACIENTE': data.patient.userInfo.city ? data.patient.userInfo.city : '',
					'ESTADO DO PACIENTE': data.patient.userInfo.state ? data.patient.userInfo.state : '',
					'DIRECIONAMENTO': data.has_been_accredited ? 'Credenciamento' : 'Agendamento',
					'SOLICITAÇÃO DE AGENDAMENTO?': isSchedule ? 'SIM' : 'NÃO',
					'STATUS': getStatus(data.status),
					'ESPECIALIDADE/EXAME': specialtyOrExam,
					'VALOR PACIENTE': !isSchedule ? formatCurrency(data?.scheduleDatesRequests?.[0]?.query_value_patient ?? 0) : '',
					'VALOR SUBSIDIADO': !isSchedule ? formatCurrency(data?.scheduleDatesRequests?.[0]?.query_value_subsidy ?? 0) : '',
					'VALOR CREDENCIADO': !isSchedule ? formatCurrency(data?.appointment?.partner?.userInfo?.accredited_value ?? 0) : '',
					'VALOR HELLOMED': !isSchedule ? formatCurrency(data?.scheduleDatesRequests?.[0]?.query_value ?? 0) : ''
				};
			});
	
			return mappedData;
		}
	
		return [];
	}, [
		page,
		limit,
		fieldSelect,
		UfValues,
		citiesValues,
		specialtyOrExamValues,
		watchOpeningDate,
		watchScheduleOrAppointment,
		appointmentStatusValues,
		scheduleStatusValues
	]);

	const exportToExcel = useCallback(async () => {
		setIsExporting(true);
	
		try {
			const exportData = await generateExportData();
	
			if (exportData.length > 0) {
				ExportDataToExcel({ data: exportData, fileName: 'Relatório de Atendimentos'});
			} else {
				console.error("Nenhum dado para exportar!");
			}
		} catch (error) {
			console.error('Erro ao exportar dados:', error);
		} finally {
			setIsExporting(false);
		}
	}, [generateExportData]);

	const exportToPdf = useCallback(async () => {
		setIsExporting(true);
	
		try {
			const exportData = await generateExportData();
	
			if (exportData.length > 0) {
				ExportDataToPDF({ data: exportData, fileName: 'Relatório de Atendimentos', orientationPdf: 'l' });
			} else {
				console.error("Nenhum dado para exportar!");
			}
		} catch (error) {
			console.error('Erro ao exportar dados:', error);
		} finally {
			setIsExporting(false);
		}
	}, [generateExportData]);


	const {data: dataReport, isLoading, error, isFetching} = useSchedulesAndAppointmentsReport({
		page,
		limit,
		targeting: fieldSelect,

		stateUFS: UfValues,
		citiesNames: citiesValues,
		specialtySecureId: specialtyOrExamValues,
		examSecureId: specialtyOrExamValues,
		openingDate: watchOpeningDate,
		scheduleOrAppointment: watchScheduleOrAppointment,
		appointmentStatus: appointmentStatusValues,
		scheduleStatus: scheduleStatusValues
	});

	const {
		data: dependenciesData,
		isLoading: isDependenciesHookLoading,
		isFetching: isDependenciesHookFetching
	} = useSchedulesAndAppointmentsDependencies();
	
	const isDependenciesLoading = isDependenciesHookFetching || isDependenciesHookLoading;

	const { 
		data: citiesData, 
		isLoading: isCitiesHookLoading,
		isFetching: isCitiesFetching,
	} = useSchedulesAndAppointmentsGetCitiesByUFS({ stateUFS: UfValues })

	const isCitiesLoading = isCitiesFetching || isCitiesHookLoading;

	useEffect(() => {
		setFieldSelect('both');
	}, []);

	const hasUF = watchUf && watchUf.length > 0;
	const hasSpecialtyOrExam = watchSpecialtyOrExam && watchSpecialtyOrExam.length > 0;
	const hasCity = watchCity && watchCity.length > 0;



	const needsBadge = hasUF || hasSpecialtyOrExam;

	const selectedUFLabels = hasUF 
	? watchUf
		.map(uf => dependenciesData?.statesOptions.find(state => state.value === uf.value)?.label)
		.filter(label => label)
		.join(', ')
	: '';

	const selectedCityLabels = hasCity
	? watchCity.map(city => city.label).join(', ')
	: '';

	const selectedSpecialtyOrExamLabels = hasSpecialtyOrExam
		? watchSpecialtyOrExam
			.map(item => dependenciesData?.specialtiesAndExamsOptions.find(option => option.value === item.value)?.label)
			.filter(label => label)
			.join(', ')
		: '';

	function handleClickUFBadge() {
		setValue('uf', []);
		setValue('city', []);

		queryClient.invalidateQueries({
			queryKey: ['SchedulesAndAppointmentsReport']
		});
	}

	function handleClickCityBadge() {
		setValue('city', []);

		queryClient.invalidateQueries({
			queryKey: ['SchedulesAndAppointmentsReport']
		});
	}

	function handleClickSpecialtyBadge() {
		setValue('specialtyOrExam', []);

		queryClient.invalidateQueries({
			queryKey: ['SchedulesAndAppointmentsReport']
		});
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<HStack w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Relatório de Agendamentos
				</Text>

        {isExporting && (
          <>
            <Spinner size="md" />
            <Text>Exportando...</Text>
          </>
        )}

				<HStack>
				<Button
					as="a"
					size="md"
					fontSize="md"
					colorScheme="whatsapp"
					cursor="pointer"
					leftIcon={<Icon as={RiFileExcel2Line} fontSize="20" />}
					onClick={exportToExcel}
					isDisabled={isExporting}
				>
					Exportar
				</Button>
				<Button
					as="a"
					size="md"
					fontSize="md"
					colorScheme="orange"
					cursor="pointer"
					leftIcon={<Icon as={PiFilePdfLight} fontSize="20" />}
					onClick={exportToPdf}
					isDisabled={isExporting}
				>
					Exportar
				</Button>
				</HStack>
			</HStack>

			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<Flex justify="space-between" align="center" w="100%">
					<Accordion w="100%" allowToggle px={0} mx={0}>
						<AccordionItem>
							<AccordionButton px={2}>
								<Box 
									as='span' 
									flex='1' 
									textAlign='left' 
									display='flex' 
									justifyContent='flex-start' 
									alignItems='center'
									gap={4}
								>
									Filtros

									<FaFilter />
								</Box>
								<AccordionIcon />
							</AccordionButton>

							<AccordionPanel
								pb={4} 
								display='flex' 
								alignItems='center'
								width='100%'
								flexDirection={isTablet ? 'column' : 'row'}
								justifyContent={isTablet ? 'center' : 'space-between'}
								// justifyContent='center'
								gap={4}
							>
								<Grid
									templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' }}
									alignItems='center'
									justifyContent='space-between'
									width='100%'
									gap={4}
								>
									<InputDate
										{...register('openingDate')}
										label="Data da Abertura"
									/>
									
									<InputSelectMult
										label="Especialidade/Exame"
										name='specialtyOrExam'
										control={control}
										placeholder="Especialidades/Exames"
										options={dependenciesData?.specialtiesAndExamsOptions || []}
										menuPortalTarget={typeof document !== 'undefined' ? document.body: null}
										styles={{
											menuPortal: (base) => ({ ...base, zIndex: 9999 }),
										}}
										isDisabled={isDependenciesLoading}
									/>

									<InputSelect
										label="Direcionamento"
										name="targeting"
										options={[
											{ label: 'Ambos', value: 'both' },
											{ label: 'Credenciamento', value: 'accreditation' },
											{ label: 'Agendamento', value: 'schedule' }
										]}
										onChange={event => setFieldSelect(event.target.value)}
										bg="blackAlpha.100"	
									/>

									<InputSelectMult
										label="UF"
										name="uf"
										placeholder="UF"
										control={control}
										options={dependenciesData?.statesOptions || []}
										menuPortalTarget={typeof document !== 'undefined' ? document.body: null}
										styles={{
											menuPortal: (base) => ({ ...base, zIndex: 9999 }),
										}}
										isDisabled={isDependenciesLoading}
									/>

									<InputSelectMult
										label="Cidade"
										name="city"
										placeholder={isCitiesLoading ? "Selecione um estado primeiro" : 'Selecione uma cidade'}
										control={control}
										options={citiesData?.cities && citiesData?.cities.length > 0 ? citiesData.cities : []}
										menuPortalTarget={typeof document !== 'undefined' ? document.body: null}
										styles={{
											menuPortal: (base) => ({ ...base, zIndex: 9999 }),
										}}
										// isDisabled={isCitiesLoading}
									/>
									<Flex flexDirection='column'>
										<InputSelect
											label="Solicitação de Agendamento"
											{...register('scheduleOrAppointment')}
											options={[
												{ label: 'Ambos', value: 'both' },
												{ label: 'Sim', value: 'isSchedule' },
												{ label: 'Não', value: 'isAppointment' }
											]}
											bg="blackAlpha.100"
										/>

										{watchScheduleOrAppointment === 'isAppointment' ?
											<InputSelectMult
												name='appointmentStatus'
												placeholder='Status de Agendamento'
												options={[
													{ label: 'Aprovado', value: 'approved' },
													{ label: 'Não compareceu', value: 'did_not_attend' },
													{ label: 'Realizado', value: 'realized' },
													{ label: 'Finalizado', value: 'finalized' },
													{ label: 'Cancelado pelo Paciente', value: 'canceled_by_patient' },
													{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
													{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
												]}
												menuPortalTarget={typeof document !== 'undefined' ? document.body: null}
												styles={{
													menuPortal: (base) => ({ ...base, zIndex: 9999 }),
												}}
												control={control}
											/>
											: watchScheduleOrAppointment === 'isSchedule' ?
											<InputSelectMult
												name='scheduleStatus'
												placeholder='Status da Solicitação'
												options={[
													{ label: 'Agenda', value: 'in_schedule' },
													{ label: 'Aguardando Backoffice Pontual', value: 'waiting_backoffice' },
													{ label: 'Aguardando Backoffice em Rede', value: 'waiting_backoffice_network' },
													{ label: "Orçamento", value: "budget" },
													{ label: 'Aguardando Paciente', value: 'waiting_patient' },
													{ label: 'Cancelado pelo Paciente', value: 'canceled_by_patient' },
													{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
													{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
													{ label: 'Em credenciamento', value: 'in_accreditation' },
													{ label: 'Sem Contato', value: 'no_contact' },
													{ label: 'Sem Interesse', value: 'no_interest' },
													{ label: 'Falta Pedido', value: 'lack_request' },
													{ label: 'Divergência de Informações', value: 'info_divergence' },
													{ label: 'Condição Financeira', value: 'financial_condition' },
													{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
												]}
												menuPortalTarget={typeof document !== 'undefined' ? document.body: null}
												styles={{
													menuPortal: (base) => ({ ...base, zIndex: 9999 }),
												}}
												control={control}
											/> : null
										}
									</Flex>
									
								</Grid>
							</AccordionPanel>
						</AccordionItem>
					</Accordion>		
				</Flex>
				</Flex>

				{needsBadge ? (
					<Flex 
						w="100%" 
						// justify="center" 
						align="center" 
						flexWrap='wrap' 
						pb={2}
						mt={2} 
						gap={4}
						borderBottomWidth={1}
						borderBottomColor="gray.200"
					>
						{hasUF ? (
							<CustomBadge title={`UF: ${selectedUFLabels}`} handleClick={handleClickUFBadge} />
						) : null}

						{hasCity ? (
							<CustomBadge title={`Cidade: ${selectedCityLabels}`} handleClick={handleClickCityBadge} />
						) : null}

						{hasSpecialtyOrExam ? (
							<CustomBadge title={`Especialidade/Exame: ${selectedSpecialtyOrExamLabels}`} handleClick={handleClickSpecialtyBadge} />
						) : null}
					</Flex>
				) : null}

				{isLoading ? (
					<Flex justify="center" w="100%" py={10}>
						<Spinner size="xl" />
					</Flex>
				) : dataReport?.data.length === 0 ? (
					<Flex justify="center" w="100%" py={10}>
						<Text>Nenhum relatório encontrado.</Text>
					</Flex>
				) : dataReport && (
					<>
						<TableContainer w="100%">
							<Table whiteSpace="pre-wrap">
								<Thead>
									<Tr>
										<Th>Data Abertura</Th>
										<Th>Paciente</Th>
										<Th>Contato</Th>
										<Th justifyContent="center">Cidade</Th>
										<Th justifyContent="center">Estado</Th>
										<Th justifyContent="center">Direcionamento</Th>
										<Th>Solicitação de Agendamento</Th>
										<Th justifyContent="center">Status</Th>
										<Th justifyContent="center">Especialidade</Th>
										<Th justifyContent="center">Valor Paciente</Th>
										<Th justifyContent="center">Valor Credenciado</Th>
										<Th justifyContent="center">Valor Subsidio</Th>
										<Th justifyContent="center">Valor Hellomed</Th>
									</Tr>
								</Thead>

								<Tbody>
									{dataReport.data.map(report => (
										<CardScheduleAndAppointments
											key={report.secure_id}
											secure_id={report.secure_id}
											createdAt={report.created_at}
											isAppointment={!!report.appointment}
											patientName={report.patient.userInfo.name}
											phoneNumber={report.patient.userInfo.cell}
											phoneDDD={report.patient.userInfo.ddd_cell}
											city={report.patient.userInfo.city}
											state={report.patient.userInfo.state}
											hasAccreditation={report.has_been_accredited}
											status={!!report.appointment ? report.appointment.status : report.status}
											specialtyOrExam={report?.specialty?.name || report?.exam?.name || ''}
											patientValue={report?.scheduleDatesRequests?.[0]?.query_value_patient ?? 0}
											accreditedValue={report?.appointment?.partner?.userInfo?.accredited_value ? report.appointment?.partner?.userInfo?.accredited_value : 0}
											subsidyValue={report?.scheduleDatesRequests?.[0]?.query_value_subsidy ? report?.scheduleDatesRequests[0]?.query_value_subsidy : 0}
											hellomedValue={report?.scheduleDatesRequests?.[0]?.query_value ? report?.scheduleDatesRequests?.[0]?.query_value : 0}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>

						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={dataReport.meta.total}
								registersInCurrentPage={dataReport.data.length}
								currentPage={dataReport.meta.current_page}
								registersPerPage={dataReport.meta.per_page}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['report_appointments_view']
})

export default ScheduleReports
