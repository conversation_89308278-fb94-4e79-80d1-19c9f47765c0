import {
	VSta<PERSON>,
	<PERSON>,
	Ta<PERSON>,
	<PERSON>b<PERSON><PERSON>,
	<PERSON>b,
	<PERSON>b<PERSON><PERSON><PERSON>,
	TabPanel,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { setupApiClient } from "~/services/api"
import { ShowUser } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { PermissionProps } from "~/utils/Types/Permissions"

import { FormEditUserAdmin } from "~/components/Admin/User/FormEditUserAdmin"
import { FormEditPermissionUserAdmin } from "~/components/Admin/User/FormEditPermissionUserAdmin"
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"

interface UsersEditProps {
	user: ShowUser
	permissions: PermissionProps[]
}

const UsersEdit: NextPage<UsersEditProps> = ({
	user,
	permissions,
}) => {
	return (
		<VStack spacing="4" layerStyle="container">
			<Box p="4" width="100%" layerStyle="card">
				<Tabs variant="enclosed" size="lg" isFitted flex="1">
					<TabList>
						<Tab textStyle="headerSM">Informações</Tab>
						<Tab textStyle="headerSM">Permissões</Tab>
					</TabList>
					<TabPanels
						layerStyle="tabContainer"
					>
						<TabPanel>
							<FormEditUserAdmin user={user} />
						</TabPanel>
						<TabPanel>
							<FormEditPermissionUserAdmin
								permissions={permissions}
								user={user}
							/>
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<ListActionLog
				layerStyle="card"
				changedSecureId={user.secure_id}
				type="user"
			/>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/admin/users/${id}`)

		const response = await api.get(`v1/admin/permissions`)

		return {
			props: {
				user: data,
				permissions: response.data,
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["users_edit"],
	}
)

export default UsersEdit
