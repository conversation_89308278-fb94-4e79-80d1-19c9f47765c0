import {
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useUsersAdmin } from "~/hooks/Admin/Users/<USER>"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardUserAdmin } from "~/components/Admin/User/CardUserAdmin"


interface UsersProps {

}

const Users: NextPage<UsersProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = useUsersAdmin({ page, search, limit })

	const userCanSeeCreate = useCan({
		permissions: ['users_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['users_edit']
	})

	const userCanSeeDelete = useCan({
		permissions: ['users_delete']
	})

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Usuários
				</Text>
				{userCanSeeCreate && (
					<ButtonToCreate linkHref="/admin/users/add">
						Novo
					</ButtonToCreate>
				)}
			</Flex>
			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome ou e-mail"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{isLoading ? (
					<Flex justify="center" w="100%" py={10}>
						<Spinner size="xl" />
					</Flex>
				) : data?.users.length === 0 ? (
					<Flex justify="center" w="100%" py={10}>
						<Text>Nenhum usuário encontrado.</Text>
					</Flex>
				) : data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										<Th>E-mail</Th>
										<Th>CPF</Th>
										<Th>Celular</Th>
										{(userCanSeeEdit || userCanSeeDelete) && (
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
										)}
									</Tr>
								</Thead>
								<Tbody>
									{data.users.map(user => (
										<CardUserAdmin
											key={user.secure_id}
											secure_id={user.secure_id}
											name={user.name}
											email={user.email}
											legal_document_number={user.legal_document_number}
											cell={user.cell}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.users.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['users_view']
})

export default Users
