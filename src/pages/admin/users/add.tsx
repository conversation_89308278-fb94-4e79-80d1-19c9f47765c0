import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Grid,
	GridItem,
	Divider,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { UserFormData } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { FormUser } from "~/components/global/FormUser"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { cpfValidation } from "~/utils/Validator/validateCpf"

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	legal_document_number: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("CPF obrigatório")
	}).test("cpf", "CPF inválido", (value) => {
		if (!value) return false;
	
		const cleanedValue = value.replace(/\D/g, "");
	
		if (cleanedValue.length === 11) {
		  return cpfValidation(cleanedValue);
		} 
	
		return false;
	  }),
	cell: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Celular obrigatório")
	}),
	birth_date: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Data de nascimento obrigatória")
	}),
	password: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema
			.required("Senha obrigatória")
			.min(8, "No mínimo 8 caracteres")
			.matches(
				/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
				'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
			)
	}),
	passwordConfirmation: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.oneOf([yup.ref("password")], "As senha precisam ser iguais")
	}),
})

interface UsersAddProps {}

const UsersAdd: NextPage<UsersAddProps> = () => {
	const toast = useToast()

	const {
		register,
		formState: { errors },
		formState,
		handleSubmit,
		watch,
		setValue,
		clearErrors
	} = useForm<UserFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			userExists: false,
		},
	})

	const add = useMutation(
		async (values: UserFormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.post("/v1/admin/users", {
				...values,
				ddd_cell,
				cell
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["UsersAdmin"])
				toast({
					title:
						response.data?.message || "Novo usuário cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar usuário.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<UserFormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<FormUser
						clearErrors={clearErrors}
						formState={formState}
						register={register}
						setValue={setValue}
						watch={watch}
						textUserExists="já possui cadastro na plataforma, para adiciona-lo como administrador clique em cadastrar."
					/>
					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(12, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<InputPassword
								label="Senha *"
								placeholder="Senha *"
								error={errors.password}
								{...register("password")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<InputPassword
								label="Confirmação de Senha *"
								placeholder="Repetir a Senha *"
								error={errors.passwordConfirmation}
								{...register("passwordConfirmation")}
							/>
						</GridItem>
					</Grid>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["users_create"],
	}
)

export default UsersAdd
