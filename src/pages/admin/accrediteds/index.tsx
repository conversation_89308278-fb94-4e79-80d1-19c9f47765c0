"use client";

import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Checkbox,
  Flex,
  HStack,
  Spinner,
  Stack,
  Table,
  TableContainer,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  VStack,
  useDisclosure,
  useMediaQuery,
  useToast,
} from "@chakra-ui/react";
import { useEffect, useMemo, useState } from "react";
import { GetServerSideProps, NextPage } from "next";
import { AxiosError, AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";

import { useCan } from "~/hooks/useCan";
import { api } from "~/services/apiClient";
import { queryClient } from "~/services/queryClient";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";
import { useControlFilters } from "~/contexts/ControlFiltersContext";

import { Pagination } from "~/components/global/Pagination";
import { InputSearch } from "~/components/global/Form/InputSearch";
import { ButtonToModal } from "~/components/global/Buttons/ButtonToModal";
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate";
import { CardAccreditedAdmin } from "~/components/Admin/Accredited/CardAccreditedAdmin";
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel";
import { ModalImportAccredited } from "~/components/Admin/Accredited/ModalImportAccredited";
import { FaFilter } from "react-icons/fa";
import { InputSelect } from "~/components/global/Form/InputSelect";
import { CustomBadge } from "~/components/Admin/Appointment/CustomBadge";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { getUserStatus } from "~/utils/translateNames/user/status";

import { useAccreditedsAdmin } from "~/hooks/Admin/Accrediteds/useAccreditedsAdmin";
import { useAccreditedsDependencies } from "~/hooks/Admin/Accrediteds/useAccreditedDependencies";
import { useAccreditedsDependenciesGetCityByEstadoUF } from "~/hooks/Admin/Accrediteds/useAccreditedCity";

interface AccreditedsProps {}

const filterSchema = yup.object().shape({
  status: yup.mixed<"active" | "inactive" | "all" | "punctual">().optional(),
  uf: yup.string().optional(),
  city: yup.string().optional(),
  specialtyOrExam: yup.string().optional(),
});

type FilterSchema = yup.InferType<typeof filterSchema>;

const Accrediteds: NextPage<AccreditedsProps> = () => {
  const toast = useToast();
  const { isOpen, onToggle } = useDisclosure();

  const { page, limit, search, setPage, setLimit, setSearch } =
    useControlFilters();

  const filterForm = useForm<FilterSchema>({
    resolver: yupResolver(filterSchema),
    defaultValues: {
      status: "all",
      uf: "Selecione um estado", 
      city: "Selecione uma cidade",
      specialtyOrExam: "Selecione uma especialidade/exame", 
    },
  });

  const {
    watch: watchFilter,
    setValue: setFilterValue,
    reset: resetFilters,
    resetField,
    register,
  } = filterForm;

  const watchStatus = watchFilter("status");
  const watchUF = watchFilter("uf");
  const watchCity = watchFilter("city");
  const watchSpecialtyOrExam = watchFilter("specialtyOrExam");

  const { data: dependenciesData, isLoading: isDependenciesLoading } =
    useAccreditedsDependencies();

  const hasActiveUF =
    watchUF && watchUF.toLowerCase() !== "selecione um estado";
  const hasActiveCity =
    watchCity && watchCity.toLowerCase() !== "selecione uma cidade";
  const hasActiveSpecialtyOrExam =
    watchSpecialtyOrExam &&
    watchSpecialtyOrExam.toLowerCase() !== "selecione uma especialidade/exame";

  const { data: citiesData, isLoading: isCitiesLoading } =
    useAccreditedsDependenciesGetCityByEstadoUF({
      stateUF: watchUF,
    });

  useEffect(() => {
    if (!hasActiveUF) {
      resetField("city");
    }
  }, [watchUF, resetField, hasActiveUF]);

  const { data, isLoading, error, isFetching } = useAccreditedsAdmin({
    page,
    search,
    limit,
    status: watchStatus,
    stateUF: hasActiveUF ? watchUF : undefined,
    cityName: hasActiveCity ? watchCity : undefined,
    specialtySecureId: hasActiveSpecialtyOrExam
      ? watchSpecialtyOrExam
      : undefined,
    examSecureId: hasActiveSpecialtyOrExam ? watchSpecialtyOrExam : undefined,
  });

  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const isTablet = useMediaQuery("(max-width: 768px)")[0];

  const userCanSeeCreate = useCan({ permissions: ["accrediteds_create"] });
  const userCanSeeEdit = useCan({ permissions: ["accrediteds_edit"] });
  const userCanSeeDelete = useCan({ permissions: ["accrediteds_delete"] });

  const dataExportExcel = useMemo(() => {
    if (data) {
      return data.accrediteds.map((accredited) => ({
        NOME: accredited.name,
        "E-MAIL": accredited.email,
        CPF: accredited.legal_document_number,
        CELULAR: accredited.cell,
        STATUS: accredited.status,
      }));
    }
    return [];
  }, [data]);

  const handleSelectAll = () => {
    if (selectedItems.length === 0 && data) {
      setSelectedItems(
        data.accrediteds.map((accredited) => accredited.secure_id)
      );
      return;
    }
    setSelectedItems([]);
  };

  const changeVibility = useMutation(
    async (showAccreditedInApp: boolean) => {
      return await api.put(`/v1/admin/visibility-accrediteds`, {
        partners: selectedItems,
        showAccreditedInApp,
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["AccreditedsAdmin"]);
        toast({
          title: response.data?.message || "Visibilidade alterada com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Erro ao alterar visibilidade do credenciado.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleChageVisibility = async (showAccreditedInApp: boolean) => {
    try {
      await changeVibility.mutateAsync(showAccreditedInApp);
      setSelectedItems([]);
    } catch {}
  };

  useEffect(() => {
    setSelectedItems([]);
  }, [page]);

  const selectedUFLabel = hasActiveUF
    ? dependenciesData?.statesOptions.find((opt) => opt.value === watchUF)
        ?.label
    : "";

  const selectedSpecialtyOrExamLabel = hasActiveSpecialtyOrExam
    ? dependenciesData?.specialtiesAndExamsOptions.find(
        (opt) => opt.value === watchSpecialtyOrExam
      )?.label
    : "";

  function handleRemoveStatusFilter() {
    setFilterValue("status", "all");
  }
  function handleRemoveUFFilter() {
    resetField("uf");
  }
  function handleRemoveCityFilter() {
    resetField("city");
  }
  function handleRemoveSpecialtyOrExamFilter() {
    resetField("specialtyOrExam");
  }

  function handleClearAllFilters() {
    resetFilters(); 
  }

  const hasActiveFilters = useMemo(() => {
    return (
      watchStatus !== "all" ||
      hasActiveUF ||
      hasActiveCity ||
      hasActiveSpecialtyOrExam
    );
  }, [watchStatus, hasActiveUF, hasActiveCity, hasActiveSpecialtyOrExam]);

  return (
    <>
      <VStack spacing="4" layerStyle="container">
        <Flex w="100%" justify="space-between" align="center">
          <Flex>
            <Text textStyle="headerLG" as="header">
              Credenciados
            </Text>
            <Text ml={2}>(Médicos/Clínicas/Laboratórios)</Text>
          </Flex>
          <HStack spacing="4">
            <ButtonExportExcel
              isDisabled={dataExportExcel.length === 0}
              data={dataExportExcel}
              fileName={`exportar_credenciados`}
            >
              Exportar
            </ButtonExportExcel>
            {userCanSeeCreate && (
              <>
                <ButtonToModal action={onToggle}>Importar</ButtonToModal>
                <ButtonToCreate linkHref="/admin/accrediteds/add">
                  Novo
                </ButtonToCreate>
              </>
            )}
          </HStack>
        </Flex>
        <VStack layerStyle="card" width="100%" p="4">
          <Flex w="100%" justify="space-between">
            <Flex>
              {!!error && (
                <Flex justify="center">
                  <Text>Falha ao obter dados.</Text>
                </Flex>
              )}
            </Flex>
            <Stack
              spacing="4"
              align="center"
              direction={{ sm: "column-reverse", lg: "row" }}
            >
              {selectedItems.length > 0 && (
                <>
                  <Button
                    size="sm"
                    fontSize="sm"
                    colorScheme="teal"
                    isLoading={changeVibility.isLoading}
                    onClick={() => handleChageVisibility(true)}
                  >
                    Desocultar no aplicativo
                  </Button>
                  <Button
                    size="sm"
                    fontSize="sm"
                    colorScheme="yellow"
                    isLoading={changeVibility.isLoading}
                    onClick={() => handleChageVisibility(false)}
                  >
                    Ocultar no aplicativo
                  </Button>
                </>
              )}
              <Box w="72">
                <InputSearch
                  name="search"
                  placeholder="Nome, e-mail ou CEP"
                  setPage={setPage}
                  setSearch={setSearch}
                />
              </Box>
            </Stack>
          </Flex>
          <Flex justify="space-between" align="center" w="100%">
            <Accordion w="100%" allowToggle px={0} mx={0}>
              <AccordionItem>
                <AccordionButton px={2}>
                  <Box
                    as="span"
                    flex="1"
                    textAlign="left"
                    display="flex"
                    justifyContent="flex-start"
                    alignItems="center"
                    gap={4}
                  >
                    Filtros <FaFilter />
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel
                  pb={4}
                  display="flex"
                  flexDirection={isTablet ? "column" : "row"}
                  justifyContent={isTablet ? "center" : "flex-start"}
                  gap={4}
                >
                  <InputSelect
                    label="Status"
                    options={[
                      { label: "Todos", value: "all" },
                      { label: "Ativo", value: "active" },
                      { label: "Inativo", value: "inactive" },
                      { label: "Pontual", value: "punctual" },
                    ]}
                    {...register("status")}
                  />
                  <InputSelect
                    label="UF"
                    options={dependenciesData?.statesOptions || []}
                    isDisabled={isDependenciesLoading}
                    {...register("uf")}
                  />
                  <InputSelect
                    label="Cidade"
                    options={hasActiveUF ? citiesData?.cities || [] : []}
                    isDisabled={!hasActiveUF || isCitiesLoading}
                    {...register("city")}
                  />
                  <InputSelect
                    label="Especialidade/Exame"
                    options={dependenciesData?.specialtiesAndExamsOptions || []}
                    isDisabled={isDependenciesLoading}
                    {...register("specialtyOrExam")}
                  />
                </AccordionPanel>
              </AccordionItem>
            </Accordion>
          </Flex>
          {hasActiveFilters && (
            <Flex
              w="100%"
              align="center"
              flexWrap="wrap"
              pb={2}
              mt={2}
              gap={2}
              borderBottomWidth={1}
              borderBottomColor="gray.200"
            >
              {watchStatus && watchStatus !== "all" && (
                <CustomBadge
                  handleClick={handleRemoveStatusFilter}
                  title={`Status: ${getUserStatus(watchStatus)}`}
                />
              )}
              {hasActiveUF && (
                <CustomBadge
                  handleClick={handleRemoveUFFilter}
                  title={`UF: ${selectedUFLabel}`}
                />
              )}
              {hasActiveCity && (
                <CustomBadge
                  handleClick={handleRemoveCityFilter}
                  title={`Cidade: ${watchCity}`}
                />
              )}
              {hasActiveSpecialtyOrExam && (
                <CustomBadge
                  handleClick={handleRemoveSpecialtyOrExamFilter}
                  title={`Especialidade/Exame: ${selectedSpecialtyOrExamLabel}`}
                />
              )}
            </Flex>
          )}
          {isLoading ? (
            <Flex justify="center" w="100%" py={10}>
              <Spinner size="xl" />
            </Flex>
          ) : data?.accrediteds.length === 0 ? (
            <Flex justify="center" w="100%" py={10}>
              <Text>Nenhum credenciado encontrado.</Text>
            </Flex>
          ) : data && (
            <>
              <TableContainer w="100%">
                <Table>
                  <Thead>
                    <Tr>
                      <Th>
                        <Checkbox
                          isChecked={
                            selectedItems.length > 0 &&
                            selectedItems.length === data.accrediteds.length
                          }
                          isIndeterminate={
                            selectedItems.length > 0 &&
                            selectedItems.length < data.accrediteds.length
                          }
                          onChange={handleSelectAll}
                        />
                      </Th>
                      <Th>Nome</Th>
                      <Th>E-mail</Th>
                      <Th>CPF</Th>
                      <Th>Status</Th>
                      <Th>Celular</Th>
                      {(userCanSeeEdit || userCanSeeDelete) && (
                        <Th>
                          <Text align="center">Ações</Text>
                        </Th>
                      )}
                    </Tr>
                  </Thead>
                  <Tbody>
                    {data.accrediteds.map((accredited) => (
                      <CardAccreditedAdmin
                        key={accredited.secure_id}
                        accredited={accredited}
                        setSelectedItems={setSelectedItems}
                        checked={
                          selectedItems.indexOf(accredited.secure_id) >= 0
                        }
                      />
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
              <Flex justify="flex-end" w="100%">
                <Pagination
                  totalCountOfRegisters={data.total}
                  registersInCurrentPage={data.accrediteds.length}
                  currentPage={data.page}
                  registersPerPage={data.perPage}
                  onPageChange={setPage}
                  limit={limit}
                  setLimit={setLimit}
                />
              </Flex>
            </>
          )}
        </VStack>
      </VStack>
      {isOpen && (
        <ModalImportAccredited isOpen={isOpen} closeModal={onToggle} />
      )}
    </>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    return {
      props: {},
    };
  },
  {
    roles: ["MASTER", "ADMIN"],
    permissions: ["accrediteds_view"],
  }
);

export default Accrediteds;
