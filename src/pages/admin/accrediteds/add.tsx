import {
  VStack,
  useToast,
  Box,
  Flex,
  HStack,
  SimpleGrid,
  Grid,
  GridItem,
  Divider,
} from "@chakra-ui/react";
import { useEffect } from "react"
import { GetServerSideProps, NextPage } from "next";

import * as yup from "yup";
import { SubmitHandler, useForm } from "react-hook-form";

import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { yupResolver } from "@hookform/resolvers/yup";

import { api } from "~/services/apiClient";
import { setupApiClient } from "~/services/api";
import { queryClient } from "~/services/queryClient";
import { UserFormData } from "~/utils/Types/Admin/User";
import { Option, typesDoctors, typesOfCares, typesStatus } from "~/utils/Types/Global";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";

import { Input } from "~/components/global/Form/Input";
import { InputRadio } from "~/components/global/Form/InputRadio";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { InputSelectMult } from "~/components/global/Form/InputSelectMult";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { InputCreatableSelect } from "~/components/global/Form/InputCreatableSelect";
import { InputPassword } from "~/components/global/Form/InputPassword";
import { FormAccredited } from "~/components/Admin/Accredited/FormAccredited";
import { InputNumberMask } from "~/components/global/Form/InputNumberMask";
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect";
import { cpfValidation } from "~/utils/Validator/validateCpf";
import { cnpjValidation } from "~/utils/Validator/validateCnpj";

type FormData = UserFormData & {
  adviceRegister: string;
  type: string;
  typeOfCare: string;
  status: string
  groupSecureId: Option
  paymentMethods: Option[];
  specialtiesSecureIds: Option[];
  examsSecureIds: Option[];
  queryValue: number;
  accreditedValue: number;
};

const FormSchema = yup.object().shape({
  userExists: yup.boolean(),
  name: yup.string().required("Razão Social obrigatório"),
  fantasyName: yup.string().required("Nome fantasia obrigatório"),
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
  password: yup
    .string()
    .required("Senha obrigatória")
    .min(8, "No mínimo 8 caracteres")
    .matches(
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
      'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
    ),
  passwordConfirmation: yup.string().when("userExists", {
    is: (value: boolean) => !value,
    then: (schema) =>
      schema.oneOf([yup.ref("password")], "As senha precisam ser iguais"),
  }),
  legal_document_number: yup.string().when("userExists", {
    is: (value: boolean) => !value,
    then: (schema) =>
      schema.required("CPF/CNPJ obrigatório"),
  })
  .test("cpf-or-cnpj", "CPF ou CNPJ inválido", (value) => {
    if (!value) return false;

    const cleanedValue = value.replace(/\D/g, "");

    if (cleanedValue.length === 11) {
      return cpfValidation(cleanedValue);
    } else if (cleanedValue.length === 14) {
      return cnpjValidation(cleanedValue);
    }

    return false;
  }),
  cell: yup.string().when("userExists", {
    is: (value: boolean) => !value,
    then: (schema) => schema.required("Celular obrigatório"),
  }),
  birth_date: yup.string().when("userExists", {
    is: (value: boolean) => !value,
    then: (schema) => schema.required("Data de nascimento obrigatória"),
  }),
  adviceRegister: yup.string().required("Registro obrigatório"),
  type: yup.string().required("Tipo"),
  typeOfCare: yup.string().required("Tipo de atendimento"),
  status: yup.string().required("Status obrigatório"),
  paymentMethods: yup
    .array()
    .of(yup.object().shape({}))
    .required("Métodos de pagamento obrigatório"),
  queryValue: yup.number().required("Valor do pagamento obrigatório"),
});

interface DoctorsAddProps {
  specialtiesOptions: Option[];
  examsOptions: Option[]
}

const DoctorsAdd: NextPage<DoctorsAddProps> = ({ specialtiesOptions, examsOptions }) => {
  const toast = useToast();

  const {
    register,
    formState,
    formState: { errors },
    handleSubmit,
    watch,
    setValue,
    clearErrors,
    control,
  } = useForm<FormData>({
    //@ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {
      userExists: false,
      typeOfCare: "in_person",
      type: "doctor",
      status: "active",
    },
  });

  const type = watch('type')

  const handleSearchGroups = async (search: string) => {
    const { data } = await api.get("v1/admin/list/groups", {
      params: {
        search,
      }
    })

    const groups = data.map((group: any) => ({ label: group.name, value: group.secure_id }))

    return groups
  }

  const add = useMutation(
    async (values: FormData) => {
      const newCell = values.cell.replace(/\D/g, "");
      const ddd_cell = newCell.slice(0, 2);
      const cell = newCell.slice(2);

      return await api.post("/v1/admin/accrediteds", {
        ...values,
        ddd_cell,
        cell,
        specialtiesSecureIds: values.specialtiesSecureIds
          ? values.specialtiesSecureIds.map((specialty) => specialty.value)
          : null,
        examsSecureIds: values.examsSecureIds
          ? values.examsSecureIds.map((exam) => exam.value)
          : null,
        paymentMethods: values.paymentMethods
          .map((paymentMethod) => paymentMethod.value)
          .toString(),
        groupSecureId: values.groupSecureId
          ? values.groupSecureId.value
          : null
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["AccreditedsAdmin"]);
        toast({
          title:
            response.data?.message ||
            "Novo credenciado cadastrado com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
        history.back();
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Ocorreu um problema ao cadastrar credenciado.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleAdd: SubmitHandler<FormData> = async (values) => {
    try {
      await add.mutateAsync(values);
    } catch {}
  };

  useEffect(() => {
    if (type !== 'lab') {
      setValue('examsSecureIds', [])
    }
  }, [type])

  return (
    <VStack spacing="4" layerStyle="container">
      <Box
        p="4"
        as="form"
        width="100%"
        layerStyle="card"
        onSubmit={handleSubmit(handleAdd)}
      >
        <VStack spacing="4" align="flex-start">
          <FormAccredited
            clearErrors={clearErrors}
            formState={formState}
            register={register as any}
            setValue={setValue as any}
            watch={watch as any}
            textUserExists="já possui cadastro na plataforma, para adiciona-lo como médico ou clínica preencha os campos abaixo e clique em cadastrar."
          />

          <Grid
            templateColumns={{
              sm: "repeat(4, 1fr)",
              md: "repeat(8, 1fr)",
              lg: "repeat(10, 1fr)",
              xl: "repeat(12, 1fr)",
              "2xl": "repeat(12, 1fr)",
            }}
            gap={6}
            w="100%"
            alignItems="center"
          >
            <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
              <Divider />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
              <Input
                placeholder="Registro *"
                label="Registro *"
                error={errors.adviceRegister}
                {...register("adviceRegister")}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
              <InputRadio
                label="Tipo de Atendimento"
                name="typeOfCare"
                control={control}
                options={typesOfCares}
                error={errors.typeOfCare}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
              <InputRadio
                label="Tipo"
                name="type"
                control={control}
                options={typesDoctors}
                error={errors.typeOfCare}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 1 }}>
              <InputRadio
                label="Status"
                name="status"
                control={control}
                options={typesStatus}
                error={errors.status}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 6.666666, xl: 8, "2xl": 8 }}>
              <InputCreatableSelect
                name="paymentMethods"
                label="Métodos de Pagamento *"
                placeholder="Digite um método de pagamento *"
                noOptionsMessage={() => ""}
                error={errors.paymentMethods as any}
                control={control}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 4, "2xl": 4 }}>
              <InputNumberMask
                setValue={setValue}
                placeholder="R$ 0,00"
                prefix="R$ "
                error={errors.queryValue}
                label="Valor Hellomed"
                name="queryValue"
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 12, "2xl": 12 }}>
              <InputNumberMask
                setValue={setValue}
                placeholder="R$ 0,00"
                prefix="R$ "
                error={errors.accreditedValue}
                label="Valor Particular"
                name="accreditedValue"
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
              <InputSelectMult
                name="specialtiesSecureIds"
                label="Especialidades"
                placeholder="Especialidades"
                options={specialtiesOptions}
                control={control}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
              <InputSelectMult
                name="examsSecureIds"
                label="Exames"
                placeholder="Exames"
                options={examsOptions}
                control={control}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
              <InputAsyncSelect
                isMulti={false}
                isClearable
                defaultOptions
                control={control}
                name="groupSecureId"
                error={errors.groupSecureId}
                label="Grupo"
                placeholder="Procure um grupo"
                handleSearch={handleSearchGroups}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
              <Divider />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
              <InputPassword
                label="Senha *"
                placeholder="Senha *"
                error={errors.password}
                {...register("password")}
              />
            </GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
              <InputPassword
                label="Confirmação de Senha *"
                placeholder="Repetir a Senha *"
                error={errors.passwordConfirmation}
                {...register("passwordConfirmation")}
              />
            </GridItem>
          </Grid>

          <Flex justify="flex-end" w="100%">
            <HStack spacing="4" width="20em">
              <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
              <ButtonSubmit isLoading={formState.isSubmitting}>
                Cadastrar
              </ButtonSubmit>
            </HStack>
          </Flex>
        </VStack>
      </Box>
    </VStack>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    const api = setupApiClient(ctx);
    const { data } = await api.get(`/v1/admin/list/specialties`);
    const { data: dataExam } = await api.get(`/v1/admin/list/exams`)

    const specialtiesOptions = data.map((specialty: any) => ({
      value: specialty.secure_id,
      label: specialty.name,
    }));

    const examsOptions = dataExam.map((exam: any) => ({
      value: exam.secure_id,
      label: exam.name,
    }))

    return {
      props: {
        specialtiesOptions,
        examsOptions
      },
    };
  },
  {
    roles: ["MASTER", "ADMIN"],
    permissions: ["accrediteds_create"],
  }
);

export default DoctorsAdd;
