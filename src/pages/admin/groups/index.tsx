import {
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useGroupsAdmin } from "~/hooks/Admin/Groups/useGroupsAdmin"
import { useControlFilters } from "~/contexts/ControlFiltersContext"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { CardGroupAdmin } from "~/components/Admin/Groups/CardGroupAdmin"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"

interface GroupsProps {

}

const Groups: NextPage<GroupsProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = useGroupsAdmin({ page, search, limit })

	const userCanSeeCreate = useCan({
		permissions: ['groups_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['groups_edit']
	})

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Grupos
				</Text>
				{userCanSeeCreate && (
					<ButtonToCreate linkHref="/admin/groups/add">
						Nova
					</ButtonToCreate>
				)}
			</Flex>
			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{isLoading ? (
					<Flex justify="center" w="100%" py={10}>
						<Spinner size="xl" />
					</Flex>
				) : data?.groups.length === 0 ? (
					<Flex justify="center" w="100%" py={10}>
						<Text>Nenhum grupo encontrado.</Text>
					</Flex>
				) : data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										{userCanSeeEdit && (
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
										)}
									</Tr>
								</Thead>
								<Tbody>
									{data.groups.map(group => (
										<CardGroupAdmin
											key={group.secure_id}
											group={group}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.groups.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['groups_view']
})

export default Groups
