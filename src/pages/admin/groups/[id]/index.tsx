import {
	VSta<PERSON>,
	<PERSON>,
	Ta<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>b,
	<PERSON>b<PERSON><PERSON><PERSON>,
	<PERSON>b<PERSON><PERSON><PERSON>,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { setupApiClient } from "~/services/api"
import { ListGroupAdmin } from "~/utils/Types/Admin/Group"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"
import { FormEditGroup } from "~/components/Admin/Groups/GroupEditTabs/FormEditGroup"
import { GroupAccreditedsTab } from "~/components/Admin/Groups/GroupEditTabs/GroupAccreditedsTab"

interface ExamEditProps {
	group: ListGroupAdmin
}

const ExamEdit: NextPage<ExamEditProps> = ({ group }) => {
	return (
		<VStack spacing="4" layerStyle="container">
			<Box p="4" width="100%" layerStyle="card">
				<Tabs variant="enclosed" size="lg" isFitted flex="1">
					<TabList>
						<Tab textStyle="headerSM">Informações</Tab>
						<Tab textStyle="headerSM">Credenciados</Tab>
					</TabList>

					<TabPanels layerStyle="tabContainer">
						<TabPanel>
							<FormEditGroup group={group} />
						</TabPanel>
						<TabPanel>
							<GroupAccreditedsTab groupId={group.secure_id} />
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<ListActionLog
				layerStyle="card"
				changedSecureId={group.secure_id}
				type="group"
			/>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/admin/groups/${id}`)

		return {
			props: {
				group: data
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["groups_edit"],
	}
)

export default ExamEdit
