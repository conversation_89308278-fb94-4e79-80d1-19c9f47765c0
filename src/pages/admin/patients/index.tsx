import {
	Accordion,
	Accordion<PERSON>utton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Flex,
	HStack,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	useMediaQuery,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"
import * as yup from 'yup';

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { usePatientsAdmin } from "~/hooks/Admin/Patients/usePatientsAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardPatientAdmin } from "~/components/Admin/Patient/CardPatientAdmin"
import { FaFilter } from "react-icons/fa"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { CustomBadge } from "~/components/global/Badge/CustomBadge";


interface PatientsProps {

}

const filterSchema = yup.object().shape({
	status: yup.mixed<'all' | 'active' | 'inactive'>().oneOf(['all', 'active', 'inactive'])
});

export type FilterSchemaType = yup.InferType<typeof filterSchema>;

const Patients: NextPage<PatientsProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters();

	const filterForm = useForm<FilterSchemaType>(
		{ 
			resolver: yupResolver(filterSchema),
			defaultValues: {
				status: 'all'
			}
		}
	);

	const watchStatus = filterForm.watch('status');

	const { data, isLoading, error, isFetching } = usePatientsAdmin({ 
		page, 
		search, 
		limit, 
		status: watchStatus || 'all'
	})

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const userCanSeeCreate = useCan({
		permissions: ['patients_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['patients_edit']
	})

	const userCanSeeDelete = useCan({
		permissions: ['patients_delete']
	})

	function handleResetFilter() {
		filterForm.reset({ status: 'all' });
	}

	function handleResetSearch() {
		setSearch('');
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Pacientes
				</Text>
				{userCanSeeCreate && (
					<ButtonToCreate linkHref="/admin/patients/add">
						Novo
					</ButtonToCreate>
				)}
			</Flex>

			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>

					<HStack spacing="4" align="center">
						<Box w={{ sm: '100%', md: "360px" }}>
							<InputSearch
								name="search"
								placeholder="Nome, e-mail, celular, CEP ou CPF"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>

				<Flex justify="space-between" align="center" w="100%">
					<Accordion w="100%" allowToggle px={0} mx={0}>
						<AccordionItem>
							<AccordionButton px={2}>
								<Box 
									as='span' 
									flex='1' 
									textAlign='left' 
									display='flex' 
									justifyContent='flex-start' 
									alignItems='center'
									gap={4}
								>
									Filtros

									<FaFilter />
								</Box>
								
								<AccordionIcon />
							</AccordionButton>

							<AccordionPanel 
								pb={4} 
								display='flex' 
								flexDirection={isTablet ? 'column' : 'row'}
								justifyContent={isTablet ? 'center' : 'space-between'}
								gap={4}
							>
								<InputSelect
									label="Status"
									options={[
										{ label: 'Todos', value: 'all' },
										{ label: 'Ativo', value: 'active' },
										{ label: 'Inativo', value: 'inactive' }
									]}
									{...filterForm.register('status')}
									isDisabled={isLoading || isFetching}
								/> 
							</AccordionPanel>
						</AccordionItem>
					</Accordion>
				</Flex>

				{watchStatus ? (
					<Flex 
							w="100%" 
							align="center" 
							flexWrap='wrap' 
							pb={2}
							mt={2} 
							gap={4}
							borderBottomWidth={1}
							borderBottomColor="gray.200"
						>
							{watchStatus === 'all' ? (
								<CustomBadge title="Status: Todos" />
							) : null}

							{watchStatus === 'active' ? (
								<CustomBadge title="Status: Ativo" handleClose={handleResetFilter} />
							) : null}

							{watchStatus === 'inactive' ? (
								<CustomBadge title="Status: Inativo" handleClose={handleResetFilter} />
							): null}

							{search ? (
								<CustomBadge title={`Busca: ${search}`} handleClose={handleResetSearch} />
							) : null}
							
						</Flex>
				) : null}

				 {isLoading ? (
          <Flex justify="center" w="100%" py={10}>
            <Spinner size="xl" />
          </Flex>
        ) : data?.patients.length === 0 ? (
          <Flex justify="center" w="100%" py={10}>
            <Text>Nenhum paciente encontrado.</Text>
          </Flex>
        ) : data && (
          <>
            <TableContainer w="100%">
              <Table>
                <Thead>
                  <Tr>
                    <Th>Nome</Th>
                    <Th>Nome do Parceiro</Th>
                    <Th>CPF</Th>
                    <Th>Celular</Th>
                    {(userCanSeeEdit || userCanSeeDelete) && (
                      <Th>
                        <Text align="center">Ações</Text>
                      </Th>
                    )}
                  </Tr>
                </Thead>
                <Tbody>
                  {data && data.patients.map(patient => (
                    <CardPatientAdmin
                      key={patient.secure_id}
                      patient={patient}
                    />
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
            <Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data?.patients.length}
								currentPage={data?.page}
								registersPerPage={data?.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
            </Flex>
          </>
        )}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['patients_view']
})

export default Patients
