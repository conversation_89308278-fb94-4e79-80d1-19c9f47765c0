import {
	VStack,
	useToast,
	<PERSON>,
	Flex,
	HStack,
	SimpleGrid,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { setupApiClient } from "~/services/api"
import { queryClient } from "~/services/queryClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User"
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates"

import { FormUser } from "~/components/global/FormUser"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

type ShowParent = ShowUser & {
	parent: {
		secure_id: string
		name: string
	}
}

type DependentFormData = UserFormData & {
	parentSecureId: string
}

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	parentSecureId: yup.string().required("Parente paciente obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	legal_document_number: yup.string().required("CPF obrigatório"),
	cell: yup.string().required("Celular obrigatório"),
})

interface DependentsEditProps {
	patientsOptions: Option[]
	dependentData: ShowParent
}

const DependentsEdit: NextPage<DependentsEditProps> = ({ patientsOptions, dependentData }) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors
	} = useForm<DependentFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: dependentData.userInfo.name,
			email: dependentData.email,
			avatarSecureId: dependentData.avatar ? dependentData.avatar.secure_id : undefined,
			birth_date: dependentData.userInfo.birth_date ? FormatDateForYearMonthDay(dependentData.userInfo.birth_date) : "",
			legal_document_number: dependentData.userInfo.legal_document_number ? String(dependentData.userInfo.legal_document_number.replace(/\D/g, '')) : '',
			cell: `${dependentData.userInfo.ddd_cell}${dependentData.userInfo.cell}`,
			zip_code: dependentData.userInfo.zip_code,
			street: dependentData.userInfo.street,
			number: dependentData.userInfo.number,
			complement: dependentData.userInfo.complement,
			neighborhood: dependentData.userInfo.neighborhood,
			city: dependentData.userInfo.city,
			state: dependentData.userInfo.state,
			parentSecureId: dependentData.parent.secure_id
		},
	})

	const edit = useMutation(
		async (values: DependentFormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.put(`/v1/admin/dependents/${dependentData.secure_id}`, {
				...values,
				ddd_cell,
				cell
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["DependentsAdmin"])
				toast({
					title:
						response.data?.message || "Dependente alterado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar dependente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<DependentFormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch { }
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleEdit)}
			>
				<VStack spacing="4" align="flex-start">
					<FormUser
						clearErrors={clearErrors}
						formState={formState}
						register={register as any}
						setValue={setValue as any}
						watch={watch as any}
						isEditForm
						textUserExists="isEditForm"
					/>
					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Salvar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { dependentId } = ctx.query

		const api = setupApiClient(ctx)
		const response = await api.get(`/v1/admin/dependents/${dependentId}`)


		return {
			props: {

				dependentData: response.data
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["patients_edit"],
	}
)

export default DependentsEdit
