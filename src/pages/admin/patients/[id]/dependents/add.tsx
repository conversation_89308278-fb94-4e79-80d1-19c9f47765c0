import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	SimpleGrid,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { setupApiClient } from "~/services/api"
import { queryClient } from "~/services/queryClient"
import { UserFormData } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { FormUser } from "~/components/global/FormUser"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { useEffect } from "react"

type DependentFormData = UserFormData & {
	parentSecureId: string
}

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	parentSecureId: yup.string().required("Títular obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	legal_document_number: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("CPF obrigatório")
	}),
	cell: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Celular obrigatório")
	}),
	birth_date: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Data de nascimento obrigatória")
	}),
})

interface DependentsAddProps {
	patientsOptions: Option[]
	holder: {
		secure_id: string;
		name: string;
		email: string;
	}
}

const DependentsAdd: NextPage<DependentsAddProps> = ({ patientsOptions, holder }) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors
	} = useForm<DependentFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			userExists: false,
			parentSecureId: String(patientsOptions[0].value),
			email: holder.email,
			password: String(Math.floor(Date.now() / 1000))
		},
	})

	const userSecureId = watch('userSecureId')
	const parentSecureId = watch('parentSecureId')

	const add = useMutation(
		async (values: DependentFormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.post("/v1/admin/dependents", {
				...values,
				ddd_cell,
				cell
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["DependentsAdmin"])
				toast({
					title:
						response.data?.message || "Novo dependente cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar dependente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<DependentFormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch { }
	}

	useEffect(() => {
		if (parentSecureId === userSecureId) {
			setValue('parentSecureId', '')
		}
	}, [userSecureId])

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<FormUser
						clearErrors={clearErrors}
						formState={formState}
						register={register as any}
						setValue={setValue as any}
						watch={watch as any}
						textUserExists="já possui cadastro na plataforma, para adiciona-lo como dependente, preencha os campos abaixo e clique em cadastrar."
					/>
					<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
						<InputSelect
							label="Titular"
							placeholder="Titular"
							options={patientsOptions.filter(parent => parent.value !== userSecureId)}
							{...register('parentSecureId')}
							isDisabled
							error={errors.parentSecureId}
						/>
					</SimpleGrid>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/admin/patients/${id}`)

		return {
			props: {
				patientsOptions: [{ value: data.secure_id, label: data.userInfo.name }],
				holder: {
					secure_id: data.secure_id,
					name: data.userInfo.name,
					email: data.email
				}
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["patients_create"],
	}
)

export default DependentsAdd
