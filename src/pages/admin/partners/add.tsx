import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Grid,
	GridItem,
	Divider,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { UserFormData } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { FormPartner } from "~/components/Admin/Partner/FormPartner"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { cpfValidation } from "~/utils/Validator/validateCpf"
import { cnpjValidation } from "~/utils/Validator/validateCnpj"

type FormData = Omit<UserFormData, "birth_date" | "cell">

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup
		.string()
		.required("Senha obrigatória")
		.min(8, "No mínimo 8 caracteres")
		.matches(
			/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
			'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
		),
	passwordConfirmation: yup.string().when("userExists", {
		is: (value: boolean) => !value,
		then: (schema) =>
			schema.oneOf([yup.ref("password")], "As senha precisam ser iguais"),
	}),
	legal_document_number: yup.string().when("userExists", {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("CPF/CNPJ obrigatório"),
	}).test("cpf-or-cnpj", "CPF ou CNPJ inválido", (value) => {
		if (!value) return false;
	
		const cleanedValue = value.replace(/\D/g, "");
	
		if (cleanedValue.length === 11) {
		  return cpfValidation(cleanedValue);
		} else if (cleanedValue.length === 14) {
		  return cnpjValidation(cleanedValue);
		}
		  
		return false;
	  }),
})

interface DoctorsAddProps {
}

const DoctorsAdd: NextPage<DoctorsAddProps> = ({}) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors,
		control,
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			userExists: false,
		},
	})

	const add = useMutation(
		async (values: FormData) => {
			return await api.post("/v1/admin/partners", {
				...values,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PartnersAdmin"])
				toast({
					title:
						response.data?.message ||
						"Novo parceiro cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar parceiro.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<FormPartner
						clearErrors={clearErrors}
						formState={formState}
						register={register as any}
						setValue={setValue as any}
						watch={watch as any}
						textUserExists="já possui cadastro na plataforma, para adiciona-lo como médico ou clínica preencha os campos abaixo e clique em cadastrar."
					/>

					<Grid
						templateColumns={{
							sm: "repeat(4, 1fr)",
							md: "repeat(8, 1fr)",
							lg: "repeat(10, 1fr)",
							xl: "repeat(12, 1fr)",
							"2xl": "repeat(12, 1fr)",
						}}
						gap={6}
						w="100%"
						alignItems="center"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
							<Divider />
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<InputPassword
								label="Senha *"
								placeholder="Senha *"
								error={errors.password}
								{...register("password")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<InputPassword
								label="Confirmação de Senha *"
								placeholder="Repetir a Senha *"
								error={errors.passwordConfirmation}
								{...register("passwordConfirmation")}
							/>
						</GridItem>
					</Grid>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["partners_create"],
	}
)

export default DoctorsAdd
