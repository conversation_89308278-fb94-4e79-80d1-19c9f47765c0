import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Grid,
	GridItem,
	Divider,
	Tabs,
	TabList,
	Tab,
	TabPanels,
	TabPanel,
} from "@chakra-ui/react"
import { useEffect } from "react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import {
	SubmitHandler,
	useForm,
} from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { setupApiClient } from "~/services/api"
import { queryClient } from "~/services/queryClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User"

import { Input } from "~/components/global/Form/Input"
import { InputMask } from "~/components/global/Form/InputMask"
import { InputDate } from "~/components/global/Form/InputDate"
import { InputImage } from "~/components/global/Form/ImputImage"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"
import { TabPatients } from "~/components/Admin/Partner/TabPatients"

type ShowDoctor = ShowUser & {
	userInfo: {
		zip_code: string
		street: string
		number: string
		complement: string
		neighborhood: string
		city: string
		state: string
	}
}

type FormData = Omit<UserFormData, "birth_date" | "cell">

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup.string().when("password", {
		is: (value: string) => !!value,
		then: (schema) => schema
			.required("Senha obrigatória")
			.min(8, "No mínimo 8 caracteres")
			.matches(
				/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
				'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
			)
	}),
	passwordConfirmation: yup
		.string()
		.oneOf([yup.ref("password")], "As senha precisam ser iguais"),
	legal_document_number: yup.string().required("CPF obrigatório"),
}, [
	["password", "password"]
])

interface PartnerEditProps {
	specialtiesOptions: Option[]
	examsOptions: Option[]
	partner: ShowDoctor
}
const PartnerEdit: NextPage<PartnerEditProps> = ({
	partner,
}) => {
	const toast = useToast()

	const getCep = async (value: string) => {
		if (value && value.length === 8) {
			api
				.get(`https://brasilapi.com.br/api/cep/v2/${value}`)
				.then((response) => {
					const data: any = response.data
					setValue("street", data.street)
					setValue("city", data.city)
					setValue("neighborhood", data.neighborhood)
					setValue("state", data.state)
				})
				.catch(() => {
					// setBairro('')
					// setCity('')
				})
		}
	}

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors,
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: partner.userInfo.name,
			email: partner.email,
			legal_document_number: partner.userInfo.legal_document_number
				? String(partner.userInfo.legal_document_number.replace(/\D/g, ""))
				: "",
			zip_code: partner.userInfo.zip_code,
			street: partner.userInfo.street,
			number: partner.userInfo.number,
			complement: partner.userInfo.complement,
			neighborhood: partner.userInfo.neighborhood,
			city: partner.userInfo.city,
			state: partner.userInfo.state,
			avatarSecureId: partner.avatar ? partner.avatar.secure_id : undefined,
		},
	})

	const zipCode = watch("zip_code")

	useEffect(() => {
		if (zipCode) {
			getCep(zipCode.replace(/[^\d]/g, ''))
		}
	}, [zipCode])

	const edit = useMutation(
		async (values: FormData) => {
			return await api.put(`/v1/admin/partners/${partner.secure_id}`, {
				...values,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PartnersAdmin"])
				queryClient.invalidateQueries(["ActionLogsAdmin", partner.secure_id])
				toast({
					title:
						response.data?.message || "Parceiro alterado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar parceiro.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<FormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box p="4" width="100%" layerStyle="card">
				<Tabs variant="enclosed" size="lg" isFitted flex="1">
					<TabList>
						<Tab textStyle="headerSM">Informações</Tab>
						<Tab textStyle="headerSM">Pacientes</Tab>
					</TabList>
					<TabPanels layerStyle="tabContainer">
						<TabPanel as="form" onSubmit={handleSubmit(handleEdit)}>
							<Grid
								templateColumns={{
									sm: "repeat(4, 1fr)",
									md: "repeat(8, 1fr)",
									lg: "repeat(10, 1fr)",
									xl: "repeat(12, 1fr)",
									"2xl": "repeat(12, 1fr)",
								}}
								gap={6}
								w="100%"
							>
								<GridItem
									colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}
									rowSpan={{ sm: 8, lg: 2 }}
								>
									<InputImage
										name="avatarSecureId"
										label="Avatar"
										watch={watch}
										setValue={setValue}
										clearErrors={clearErrors}
										maxHeight="200px"
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
									<Input
										placeholder="Nome *"
										label="Nome *"
										error={errors.name}
										{...register("name")}
									/>
								</GridItem>

								<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
									<Input
										placeholder="E-mail *"
										label="E-mail *"
										type="email"
										error={errors.email}
										{...register("email")}
									/>
								</GridItem>

								<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
									<Input
										label="CPF/CNPJ *"
										placeholder="CPF/CNPJ *"
										error={errors.legal_document_number}
										{...register("legal_document_number")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
									<Divider />
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, "2xl": 2 }}>
									<Input
										placeholder="Cep"
										label="Cep"
										error={errors.zip_code}
										{...register("zip_code")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, "2xl": 8 }}>
									<Input
										placeholder="Endereço"
										label="Endereço"
										error={errors.street}
										{...register("street")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, "2xl": 2 }}>
									<Input
										placeholder="Número"
										label="Número"
										error={errors.number}
										{...register("number")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, "2xl": 4 }}>
									<Input
										placeholder="Complemento"
										label="Complemento"
										error={errors.complement}
										{...register("complement")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 3 }}>
									<Input
										placeholder="Bairro"
										label="Bairro"
										error={errors.neighborhood}
										{...register("neighborhood")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
									<Input
										placeholder="Cidade"
										label="Cidade"
										error={errors.city}
										{...register("city")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 2 }}>
									<Input
										placeholder="Estado"
										label="Estado"
										error={errors.state}
										{...register("state")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
									<Divider />
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
									<InputPassword
										label="Senha"
										placeholder="Senha"
										error={errors.password}
										{...register("password")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
									<InputPassword
										label="Confirmação de Senha"
										placeholder="Repetir a Senha"
										error={errors.passwordConfirmation}
										{...register("passwordConfirmation")}
									/>
								</GridItem>
							</Grid>

							<Flex justify="flex-end" w="100%" mt={6}>
								<HStack spacing="4" width="20em">
									<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
									<ButtonSubmit
										type="submit"
										isLoading={formState.isSubmitting}
									>
										Salvar
									</ButtonSubmit>
								</HStack>
							</Flex>
						</TabPanel>
						<TabPanel>
							<TabPatients partnerId={partner.secure_id} />
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<ListActionLog
				layerStyle="card"
				changedSecureId={partner.secure_id}
				type="user"
			/>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)

		const response = await api.get(`/v1/admin/partners/${id}`)

		return {
			props: {
				partner: response.data,
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["partners_edit"],
	}
)

export default PartnerEdit
