import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Badge,
	Box,
	Flex,
	HStack,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	useMediaQuery,
	VStack
} from "@chakra-ui/react"
import { FaFilter } from "react-icons/fa";
import { GetServerSideProps, NextPage } from "next"

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useAppointmentAdmin } from "~/hooks/Admin/Appointment/useAppointmentAdmin"
import { useAppointmentsDependenciesGetCityByEstadoUF } from "~/hooks/Admin/Appointment/useAppointmentCity"

import { CardAppointmentAdmin } from "~/components/Admin/Appointment/CardAppointmentAdmin"
import { ButtonSortTable } from "~/components/global/Buttons/ButtonSortTable"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { Pagination } from "~/components/global/Pagination"
import { Input } from "~/components/global/Form/Input"
import { CustomBadge } from "~/components/Admin/Appointment/CustomBadge";
import { useForm } from "react-hook-form";
import * as yup from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { useAppointmentDependencies } from "~/hooks/Admin/Appointment/useAppointmentDependencies";
import { queryClient } from "~/services/queryClient";
import { useEffect, useState } from "react";

interface AppointmentProps {

}

const filterSchema = yup.object().shape({
	uf: yup.string().optional(),
	specialtyOrExam: yup.string().optional(),
	city: yup.string().optional()
})

type FilterSchemaType = yup.InferType<typeof filterSchema>

const Appointment: NextPage<AppointmentProps> = () => {
	const { 
		page, 
		limit, 
		search, 
		setPage, 
		setLimit, 
		setSearch, 
		direction, 
		field, 
		setField, 
		setDirection, 
		fieldSelect, 
		setFieldSelect 
	} = useControlFilters()

	const userHasPermissionView = useCan({
		permissions: ['action_logs_view']
	})

	const [currentStatus, setCurrentStatus] = useState<string>("all")

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const filterForm = useForm<FilterSchemaType>(
		{ 
			resolver: yupResolver(filterSchema),
			defaultValues: {
				uf: 'Selecione um estado',
				specialtyOrExam: 'Selecione uma especialidade/exame',
				city: 'Selecione uma cidade',
			}
		}
	);

	const watchUF = filterForm.watch('uf');
	const watchSpecialtyOrExam = filterForm.watch('specialtyOrExam');
	const watchCity = filterForm.watch('city')

	const {
		data: dependenciesData,
		isLoading: isDependenciesHookLoading,
		isFetching: isDependenciesHookFetching
	} = useAppointmentDependencies();

	const isDependenciesLoading = isDependenciesHookFetching || isDependenciesHookLoading;

	const { 
		data: citiesData, 
		isLoading: isCitiesHookLoading,
		isFetching: isCitiesFetching,
	} = useAppointmentsDependenciesGetCityByEstadoUF({ stateUF: watchUF })

	const isCitiesLoading = isCitiesFetching || isCitiesHookLoading;

	const { data, isLoading, error, isFetching } = useAppointmentAdmin({
		page,
		search,
		limit,
		direction,
		field: !!field && field.length ? field : 'date',
		status: fieldSelect,
		currentStatus: currentStatus,

		stateUF: watchUF,
		cityName: watchCity,
		specialtySecureId: watchSpecialtyOrExam,
		examSecureId: watchSpecialtyOrExam,
	});

	const hasUF = watchUF && watchUF?.toLocaleLowerCase() !== 'selecione um estado';
	
	const hasSpecialty = watchSpecialtyOrExam && watchSpecialtyOrExam?.toLocaleLowerCase() !== 'selecione uma especialidade/exame';

	const selectedUFLabel = hasUF && dependenciesData?.statesOptions.find(state => state.value === watchUF)?.label;
	const selectedSpecialtyOrExamLabel = hasSpecialty && dependenciesData?.specialtiesAndExamsOptions.find(specialtyOrExam => specialtyOrExam.value === watchSpecialtyOrExam)?.label;

	const hasCity = watchCity && watchCity?.toLocaleLowerCase() !== 'selecione uma cidade';

	const needsBadge = hasUF || hasSpecialty;

	function handleClickUFBadge() {
		filterForm.resetField('uf');
		filterForm.resetField('city');

		queryClient.invalidateQueries({
			queryKey: ['AppointmentAdmin']
		});
	}
	
	function handleClickSpecialtyBadge() {
		filterForm.resetField('specialtyOrExam');

		queryClient.invalidateQueries({
			queryKey: ['AppointmentAdmin']
		});
	}

	function handleClickCityBadge() {
		filterForm.resetField('city');

		queryClient.invalidateQueries({
			queryKey: ['AppointmentAdmin']
		});
	}

	useEffect(() => {
		setFieldSelect('all');
	}, []);

	return (
		<VStack spacing="4" layerStyle="container">
			<HStack w="100%" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Agendamentos
				</Text>
			</HStack>

			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
				</Flex>

				<Flex justify="space-between" align="center" w="100%">
					<HStack gap='3'>
						<InputSelect
							width='12em'
							name="currentStatus"
							options={[
								{ label: 'Todos', value: 'all' },
								{ label: 'Aberto', value: 'open' },
								{ label: 'Encerrado', value: 'closed' },
							]}
							onChange={(event) => setCurrentStatus(event.target.value)}
							bg="blackAlpha.100"
						/>
						<InputSelect
							width='20em'
							name="status"
							options={[
								{ label: 'Todos', value: 'all' },
								{ label: 'Aprovado', value: 'approved' },
								{ label: 'Não compareceu', value: 'did_not_attend' },
								{ label: 'Realizado', value: 'realized' },
								{ label: 'Finalizado', value: 'finalized' },
								{ label: 'Cancelado pelo Paciente', value: 'canceled_by_Patient' },
								{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
								{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
							]}
							onChange={event => setFieldSelect(event.target.value)}
							bg="blackAlpha.100"
						/>
					</HStack>

					<Box>
						<InputSearch
							name="search"
							placeholder="Nome ou CPF"
							setPage={setPage}
							setSearch={setSearch}
						/>
					</Box>
				</Flex>
				
				<Flex justify="space-between" align="center" w="100%">
					<Accordion w="100%" allowToggle px={0} mx={0}>
						<AccordionItem>
							<AccordionButton px={2}>
								<Box 
									as='span' 
									flex='1' 
									textAlign='left' 
									display='flex' 
									justifyContent='flex-start' 
									alignItems='center'
									gap={4}
								>
									Filtros

									<FaFilter />
								</Box>
								<AccordionIcon />
							</AccordionButton>

							<AccordionPanel 
								pb={4} 
								display='flex' 
								flexDirection={isTablet ? 'column' : 'row'}
								justifyContent={isTablet ? 'center' : 'space-between'}
								gap={4}
							>
								<InputSelect
									label="UF"
									options={dependenciesData?.statesOptions || []}
									{...filterForm.register('uf')}
									isDisabled={isDependenciesLoading}
								/>
							
								<InputSelect
									label="Cidade"
									options={citiesData?.cities && citiesData?.cities.length > 0 ? citiesData.cities : [{ label: 'Selecione um estado primeiro', value: 'Selecione um estado primeiro' }]}
									isDisabled={!selectedUFLabel || isCitiesLoading}
									{...filterForm.register('city')}
								/>
							
								<InputSelect
									label="Especialidade/Exame"
									options={dependenciesData?.specialtiesAndExamsOptions || []}
									{...filterForm.register('specialtyOrExam')}
									isDisabled={isDependenciesLoading}
								/>
							</AccordionPanel>
						</AccordionItem>
					</Accordion>
				</Flex>

				{needsBadge ? (
					<Flex 
						w="100%" 
						// justify="center" 
						align="center" 
						flexWrap='wrap' 
						pb={2}
						mt={2} 
						gap={4}
						borderBottomWidth={1}
						borderBottomColor="gray.200"
					>
						{hasUF ? (
							<CustomBadge title={`UF: ${selectedUFLabel}`} handleClick={handleClickUFBadge} />
						) : null}

						{hasCity ? (
							<CustomBadge title={`Cidade: ${watchCity}`} handleClick={handleClickCityBadge} />
						) : null}

						{hasSpecialty ? (
							<CustomBadge title={`Especialidade/Exame: ${selectedSpecialtyOrExamLabel}`} handleClick={handleClickSpecialtyBadge} />
						) : null}
					</Flex>
				) : null}

				{isLoading ? (
					<Flex justify="center" w="100%" py={10}>
						<Spinner size="xl" />
					</Flex>
				) : data?.appointments.length === 0 ? (
					<Flex justify="center" w="100%" py={10}>
						<Text>Nenhum agendamento encontrado.</Text>
					</Flex>
				) : data && (
					<>
						<TableContainer w="100%">
							<Table whiteSpace="pre-wrap">
								<Thead>
									<Tr>
										<Th>Data/horário</Th>
										<Th>Data/horário (Abertura)</Th>
										<Th>Paciente</Th>
										<Th>Especialidade/Exame</Th>
										<Th>
											<Flex>
												<ButtonSortTable
													label="Status Atual"
													field="status"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex>
										</Th>
										<Th>
											<Flex>
												<ButtonSortTable
													label="Sub-Status"
													field="status"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex>
										</Th>
										{userHasPermissionView && (
											<Th position='sticky' right='0' backgroundColor='white' textAlign='center'>
												Ações
											</Th>
										)}
									</Tr>
								</Thead>
								<Tbody>
									{data.appointments.map(appointment => (
										<CardAppointmentAdmin
											key={appointment?.secure_id}
											secure_id={appointment?.secure_id}
											date={appointment?.date}
											patient={appointment?.patient.userInfo.name}
											specialtyOrExam={appointment?.specialty ? appointment.specialty.name : appointment.exam!.name}
											currentStatus={appointment.current_status}
											status={appointment?.status}
											createdAt={appointment?.created_at}
											scheduleSecureId={appointment?.schedule?.secure_id}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.appointments.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['appointment_view']
})

export default Appointment
