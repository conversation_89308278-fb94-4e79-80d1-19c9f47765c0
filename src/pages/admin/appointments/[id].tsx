import {
	VStack
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query";
import { GetServerSideProps, NextPage } from "next"



import { api } from "~/services/apiClient";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

interface ScheduleEditProps {
	scheduleSecureId: string;
}

const ScheduleEdit: NextPage<ScheduleEditProps> = ({ scheduleSecureId }) => {

	const { data: scheduleData, isLoading } = useQuery(["schedules", scheduleSecureId], async () => {
		const response = await api.get('/v1/admin/schedule')
		return response.data
	})

	return (
		<VStack spacing="4" layerStyle="container">

		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		return {
			props: {
				scheduleSecureId: id
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["schedule_edit"],
	}
)

export default ScheduleEdit
