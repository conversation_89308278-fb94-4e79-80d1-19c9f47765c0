import type { AppProps } from "next/app";
import { ChakraProvider } from "@chakra-ui/react";
import Head from "next/head";
import { QueryClientProvider } from "@tanstack/react-query";

import { AppProvider } from "~/contexts";
import { queryClient } from "~/services/queryClient";

import { theme } from "~/styles/theme";
import BaseLayout from "./_layouts/baseLayout";

export default function App({ Component, pageProps, router }: AppProps) {
  const paths = ["admin", "doctor", "partner"];
  return (
    <>
      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Hello Med</title>
      </Head>
      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <AppProvider>
            {paths.some((path) => path === router.asPath.split("/")[1]) ? (
              <BaseLayout>
                {/* @ts-ignore */}
                <Component {...pageProps} />
              </BaseLayout>
            ) : (
              //@ts-ignore
              <Component {...pageProps} />
            )}
          </AppProvider>
        </ChakraProvider>
      </QueryClientProvider>
    </>
  );
}
