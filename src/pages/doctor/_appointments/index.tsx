import {
  Box,
  Flex,
  <PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  Table,
  TableContainer,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  VStack,
} from "@chakra-ui/react";
import { GetServerSideProps, NextPage } from "next";

import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";
import { useControlFilters } from "~/contexts/ControlFiltersContext";

import { Pagination } from "~/components/global/Pagination";
import { InputSearch } from "~/components/global/Form/InputSearch";
import { CardAppointmentDoctor } from "~/components/Doctor/Appointment/CardAppointmentDoctor";
import { useAppointmentDoctor } from "~/hooks/Doctor/Appointment/useAppointmentDoctor";
import { useCallback, useEffect } from "react";
import { AxiosResponse } from "axios";
import { api } from "~/services/apiClient";
import { useRouter } from "next/router";
import { useVideoCallAuthContext } from "~/contexts/VideoCallAuthContext";

interface AppointmentProps {}

const Appointment: NextPage<AppointmentProps> = () => {
  const { page, limit, search, setPage, setLimit, setSearch } =
    useControlFilters();

  const { data, isLoading, error, isFetching } = useAppointmentDoctor({
    page,
    search,
    limit,
  });
  const { setVideoCredentials, roomName, roomContext } =
    useVideoCallAuthContext();
  const router = useRouter();

  const handleSubmitVideoCallLobby = useCallback(
    async (secureId: string, userName: string) => {
      try {
        if (roomName !== secureId) {
          const response: AxiosResponse = await api.post(
            "/v1/public/join-room",
            {
              roomName: secureId,
            }
          );
          setVideoCredentials({
            token: response.data.token,
            roomName: secureId,
            userName,
          });
        }
        router.push(`/doctor/appointments/call`);
      } catch (error: any) {}
    },
    [router, setVideoCredentials]
  );

  useEffect(() => {
    disconnect();
  }, []);

  const disconnect = () => {
    roomContext?.disconnect();
    roomContext?.localParticipant.videoTracks.forEach((track) => {
      track.track.stop();
      track.unpublish();
      track.track.disable();
    });
    roomContext?.localParticipant.audioTracks.forEach((track) => {
      track.track.stop();
      track.unpublish();
      track.track.disable();
    });
  };

  return (
    <VStack spacing="4" layerStyle="container">
      <Flex w="100%" justify="space-between" align="center">
        <Text textStyle="headerLG" as="header">
          Agendamentos
        </Text>
      </Flex>
      <VStack layerStyle="card" width="100%" p="4">
        <Flex w="100%" justify="space-between">
          <Flex>
            {!!error && (
              <Flex justify="center">
                <Text>Falha ao obter dados.</Text>
              </Flex>
            )}
          </Flex>
          <HStack spacing="4" align="center">
            {isFetching && !isLoading && <Spinner />}
            <Box w="70">
              <InputSearch
                name="search"
                placeholder="Nome"
                setPage={setPage}
                setSearch={setSearch}
              />
            </Box>
          </HStack>
        </Flex>
        {data && (
          <>
            <TableContainer w="100%">
              <Table>
                <Thead>
                  <Tr>
                    <Th>Data/horário</Th>
                    <Th>Paciente</Th>
                    <Th>Tipo</Th>
                    <Th>Especialidade/Exame</Th>
                    <Th>
                      <Text>Status</Text>
                    </Th>

                    <Th>
                      <Text align="center">Ações</Text>
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {data?.appointments.map((appointment) => (
                    <CardAppointmentDoctor
                      key={appointment.secure_id}
                      secure_id={appointment.secure_id}
                      date={appointment.date}
                      patient={appointment.patient.userInfo.name}
                      type={appointment.schedule.type_consult}
                      specialtyOrExam={
                        appointment.specialty
                          ? appointment.specialty.name
                          : appointment.exam!.name
                      }
                      status={appointment.status}
                      handleSubmitVideoCallLobby={() =>
                        handleSubmitVideoCallLobby(
                          appointment.secure_id,
                          appointment.patient.userInfo.name
                        )
                      }
                    />
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
            <Flex justify="flex-end" w="100%">
              <Pagination
                totalCountOfRegisters={data.total}
                registersInCurrentPage={data.appointments.length}
                currentPage={data.page}
                registersPerPage={data.perPage}
                onPageChange={setPage}
                limit={limit}
                setLimit={setLimit}
              />
            </Flex>
          </>
        )}
      </VStack>
    </VStack>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    return {
      props: {},
    };
  },
  {
    roles: ["ACCREDITED"],
  }
);

export default Appointment;
