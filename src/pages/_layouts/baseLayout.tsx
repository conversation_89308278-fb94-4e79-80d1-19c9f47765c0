import { Box, Flex } from "@chakra-ui/react";
import { FC, ReactNode } from "react";

import { HeaderAdmin } from "~/components/Admin/Header";
import { Sidebar } from "~/components/global/Sidebar";

interface BaseLayoutProps {
  children?: ReactNode;
}

const BaseLayout: FC<BaseLayoutProps> = ({ children }) => {
  return (
    <Box minH="100vh" id="top">
      <HeaderAdmin />
      <Flex>
        <Box>
          <Sidebar />
        </Box>
        <Box
          p={{ sm: 4, md: 6, lg: 10 }}
          w={{ sm: "100%", xl: "calc(100% - 16rem)" }}
          ml="auto"
        >
          {children}
        </Box>
      </Flex>
    </Box>
  );
};

export default BaseLayout;
