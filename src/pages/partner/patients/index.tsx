import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Flex,
	HStack,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	useDisclosure,
	useMediaQuery,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"
import * as yup from 'yup';

import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { FaFilter } from "react-icons/fa"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { CustomBadge } from "~/components/global/Badge/CustomBadge";
import { usePatientsPartner } from "~/hooks/Partner/Patients/usePatientsPartner";
import { CardPatientPartner } from "~/components/Partner/Patient/CardPatientPartner";
import { useMemo } from "react";
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel";
import { ButtonToModal } from "~/components/global/Buttons/ButtonToModal";
import { ModalImportPartnerPatient } from "~/components/Partner/Patient/ModalImportPartnerPatients";


interface PartnerPatientsProps {
}

const filterSchema = yup.object().shape({
	status: yup.mixed<'all' | 'active' | 'inactive'>().oneOf(['all', 'active', 'inactive'])
});

export type FilterSchemaType = yup.InferType<typeof filterSchema>;

const PartnerPatients: NextPage<PartnerPatientsProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters();
	const { isOpen, onToggle } = useDisclosure()

	const filterForm = useForm<FilterSchemaType>(
		{ 
			resolver: yupResolver(filterSchema),
			defaultValues: {
				status: 'all'
			}
		}
	);

	const watchStatus = filterForm.watch('status');

	const { data, isLoading, error, isFetching } = usePatientsPartner({ 
		page, 
		search, 
		limit, 
		status: watchStatus || 'all'
	})

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const dataExportExcel = useMemo(() => {
		if (data) {
			return data.patients.map(patient => ({
				'NOME': patient.name,
			}))
		}

		return []
	}, [data])

	function handleResetFilter() {
		filterForm.reset({ status: 'all' });
	}

	function handleResetSearch() {
		setSearch('');
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Pacientes
				</Text>
				<HStack spacing="4">
					<ButtonExportExcel
								isDisabled={dataExportExcel.length === 0}
								data={dataExportExcel}
								fileName={`exportar_pacientes`}
							>
								Exportar
					</ButtonExportExcel>
					<ButtonToModal
						action={onToggle}
					>
						Importar
					</ButtonToModal>
				</HStack>
					{/* <ButtonToCreate linkHref="/partner/patients/add">
						Novo
					</ButtonToCreate> */}
			</Flex>

			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>

					<HStack spacing="4" align="center">
						{isFetching && !isLoading && (
							<Spinner />
						)}
						<Box w={{ sm: '100%', md: "360px" }}>
							<InputSearch
								name="search"
								placeholder="Nome, e-mail, celular, CEP ou CPF"
								setPage={setPage}
								setSearch={setSearch}
								value={search}
							/>
						</Box>
					</HStack>
				</Flex>

				<Flex justify="space-between" align="center" w="100%">
					<Accordion w="100%" allowToggle px={0} mx={0}>
						<AccordionItem>
							<AccordionButton px={2}>
								<Box 
									as='span' 
									flex='1' 
									textAlign='left' 
									display='flex' 
									justifyContent='flex-start' 
									alignItems='center'
									gap={4}
								>
									Filtros

									<FaFilter />
								</Box>
								
								<AccordionIcon />
							</AccordionButton>

							<AccordionPanel 
								pb={4} 
								display='flex' 
								flexDirection={isTablet ? 'column' : 'row'}
								justifyContent={isTablet ? 'center' : 'space-between'}
								gap={4}
							>
								<InputSelect
									label="Status"
									options={[
										{ label: 'Todos', value: 'all' },
										{ label: 'Ativo', value: 'active' },
										{ label: 'Inativo', value: 'inactive' }
									]}
									{...filterForm.register('status')}
									isDisabled={isLoading || isFetching}
								/> 
							</AccordionPanel>
						</AccordionItem>
					</Accordion>
				</Flex>

				{watchStatus ? (
					<Flex 
							w="100%" 
							align="center" 
							flexWrap='wrap' 
							pb={2}
							mt={2} 
							gap={4}
							borderBottomWidth={1}
							borderBottomColor="gray.200"
						>
							{watchStatus === 'all' ? (
								<CustomBadge title="Status: Todos" />
							) : null}

							{watchStatus === 'active' ? (
								<CustomBadge title="Status: Ativo" handleClose={handleResetFilter} />
							) : null}

							{watchStatus === 'inactive' ? (
								<CustomBadge title="Status: Inativo" handleClose={handleResetFilter} />
							): null}

							{search ? (
								<CustomBadge title={`Busca: ${search}`} handleClose={handleResetSearch} />
							) : null}
							
						</Flex>
				) : null}

				{data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										<Th>Nome do Parceiro</Th>
										<Th>CPF</Th>
										<Th>Celular</Th>
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
									</Tr>
								</Thead>
								<Tbody>
									{data.patients.map(patient => (
										<CardPatientPartner
											key={patient.secure_id}
											patient={patient}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.patients.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
			{isOpen && (
				<ModalImportPartnerPatient
					isOpen={isOpen}
					closeModal={onToggle}
				/>
			)}
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {

	return {
		props: {
		}
	}
}, {
	roles: ['PARTNER'],
})

export default PartnerPatients
