import { Flex, Image, Text, VStack, Link as ChakraLink } from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { WithSSRGuest } from "~/utils/Validator/WithSSRGuest"

import { Input } from "~/components/global/Form/Input"
import { useAuthContext } from "~/contexts/AuthContext"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import Link from "next/link"

const FormSchema = yup.object().shape({
	email: yup.string().required('E-mail obrigatório').email('E-mail inválido'),
	password: yup.string().required('Senha obrigatória')
})

type FormData = {
	email: string;
	password: string;
}

interface LoginProps {

}

const Login: NextPage<LoginProps> = () => {
	const { signIn } = useAuthContext()

	const { register, handleSubmit, formState } = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
	})

	const { errors } = formState

	const handleLogin: SubmitHandler<FormData> = async (data) => {
		try {
			await signIn(data)
		} catch { }
	}

	return (
		<Flex
			h="100vh"
			align="center"
			justify="center"

		>
			<VStack
				as="form"
				p="6"
				w="400px"
				layerStyle="card"
				onSubmit={handleSubmit(handleLogin)}
			>
				<Image
					src="/hellomed.png"
					alt="logo"
					maxW="300px"
					objectFit="contain"
				/>
				<VStack
					w="100%"
					spacing="4"
				>
					<Input
						type="email"
						label="E-mail"
						placeholder="Digite seu e-mail"
						error={errors.email}
						{...register('email')}
					/>
					<Input
						type="password"
						label="Senha"
						placeholder="Digite sua senha"
						error={errors.password}
						{...register('password')}
					/>
					<ChakraLink
						as={Link}
						textDecor="underline"
						href="/recovery-password"
						_hover={{
							color: 'blue.500'
						}}
					>
						Esqueci minha senha
					</ChakraLink>
					<VStack w="100%">
						<ButtonSubmit
							isLoading={formState.isSubmitting}
							loadingText="Entrando..."
						>
							Entrar
						</ButtonSubmit>
					</VStack>
				</VStack>
			</VStack>
		</Flex>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRGuest(async (ctx) => {
	return {
		props: {
		}
	}
})

export default Login
