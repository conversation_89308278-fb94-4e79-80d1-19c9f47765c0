import {
	<PERSON><PERSON>,
	Link as ChakraLink,
	Text,
	VStack,
	useToast,
	Image,
} from "@chakra-ui/react"
import Link from "next/link"
import { NextPage } from "next"
import { useRouter } from "next/router"

import * as yup from "yup"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"

import { Input } from "~/components/global/Form/Input"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"

const FormSchema = yup.object().shape({
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
})

type FormData = {
	email: string
}

interface RecoveryPasswordProps { }

const RecoveryPassword: NextPage<RecoveryPasswordProps> = () => {
	const toast = useToast()
	const router = useRouter()

	const { register, handleSubmit, formState } = useForm<FormData>({
		resolver: yupResolver(FormSchema),
	})
	const { errors } = formState

	const handleSubmitRecovery = async (data: FormData) => {
		try {
			const response = await api.post("/v1/recovery_password", {
				...data,
				redirectUrl: `${process.env.NEXT_PUBLIC_HOST_URL}/reset-password-admin`,
			})
			toast({
				title: response.data?.message || "Link enviado com sucesso",
				position: "top-right",
				status: "success",
				isClosable: true,
			})
			router.push("/login")
		} catch (error: any) {
			toast({
				title: error?.response.data.message || "Erro ao enviar link",
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	}

	return (
		<Flex
			h="100vh"
			align="center"
			justify="center"
		>
			<VStack
				as="form"
				p="6"
				w="400px"
				spacing="5"
				layerStyle="card"
				onSubmit={handleSubmit(handleSubmitRecovery)}
			>
				<Image
					src="/hellomed.png"
					alt="logo"
					maxW="300px"
					objectFit="contain"
				/>
				<Text textStyle="headerXL">
					Recuperar senha
				</Text>
				<Text textStyle="textXL">
					Um link para recuperar a senha será enviado para o seu email.
				</Text>
				<VStack w="100%">
					<VStack w="100%">
						<Input
							type="email"
							label="E-mail"
							placeholder="Digite seu e-mail"
							error={errors.email}
							{...register("email")}
						/>
					</VStack>
					<ChakraLink
						as={Link}
						textDecor="underline"
						href="/login"
						_hover={{
							color: 'blue.500'
						}}
					>
						Voltar a tela de login
					</ChakraLink>
					<VStack w="100%">
						<ButtonSubmit
							isLoading={formState.isSubmitting}
							loadingText="Enviando..."
						>
							Enviar
						</ButtonSubmit>
					</VStack>
				</VStack>
			</VStack>
		</Flex>
	)
}

export default RecoveryPassword
