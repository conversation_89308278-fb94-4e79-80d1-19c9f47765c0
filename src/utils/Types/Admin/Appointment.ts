export type ListAppointmentAdmin = {
	secure_id: string;
	date: string;
	current_status: 'open' | 'closed'
	status: 'approved' | 'did_not_attend' | 'realized' | 'canceled_by_Patient' | 'canceled_at_patient_request' | 'canceled_by_backoffice'
	schedule: {
		type_consult: 'in_person' | 'video_call' | 'exam';
		status: 'waiting_backoffice' | 'waiting_patient' | 'approved' | 'canceled';
		secure_id: string;
	};
	specialty?: {
		name: string;
	};
	exam?: {
		name: string;
	};
	patient: {
		userInfo: {
			name: string;
		};
	};

	created_at: string;
}
