export type ListAppointmentDashboard = {
	secure_id: string
	patient: string
	partner: string
	status: string
	expiration_time: {
		type: 'ok' | 'warning' | 'danger',
		message: string
	}
}

export type InCredentialSchedulesDashboard = {
	secure_id: string,
	followUpDate: string,
	type_consult: string;
	neighborhood: string
	city: string;
	state: string;
	date_canceled: string;
	created_at: string;
	updated_at: string;
	
	specialty: {
			name: string;
	} | null,

	exam: {
			name: string;
	} | null,

	patient: {
			userInfo: {
					name: string;
			}
	}
}

export type ResponseAppointmentDashboard = {
	secure_id: string
	status: string
	created_at: string
	patient: {
		secure_id: string
		email: string
		userInfo: {
			name: string
		}
		partners: {
			secure_id: string
			email: string
			userInfo: {
				name: string
			}
		}[]
	}
}
