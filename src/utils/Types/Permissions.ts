const permissions = [
	'users_create',
	'users_view',
	'users_edit',
	'users_delete',
	'patients_create',
	'patients_view',
	'patients_edit',
	'patients_delete',
	'partners_create',
	'partners_view',
	'partners_edit',
	'partners_delete',
	'specialties_create',
	'specialties_view',
	'specialties_edit',
	'specialties_delete',
	'schedule_view',
	'schedule_edit',
	'exams_create',
	'exams_view',
	'exams_edit',
	'schedule_create',
	'schedule_view',
	'schedule_edit',
	'schedule_delete',
	'appointment_create',
	'appointment_view',
	'appointment_edit',
	'appointment_delete',
	'notifications_create',
	'templatenotification_create',
	'templatenotification_view',
	'templatenotification_edit',
	'templatenotification_delete',
	'accrediteds_create',
	'accrediteds_view',
	'accrediteds_edit',
	'accrediteds_delete',
	'action_logs_view',
	'groups_create',
	'groups_view',
	'groups_edit',
	'groups_delete',
	'report_appointments_view',
] as const

export type PERMISSIONS = typeof permissions[number]

export type PermissionProps = {
	name: string
	slug: string
	group: string
	description: string
}
