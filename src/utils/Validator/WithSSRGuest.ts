import {
  GetServerSideProps,
  GetServerSidePropsContext,
  GetServerSidePropsResult,
} from "next";
import { destroyCookie, parseCookies } from "nookies";
import { setupApiClient } from "~/services/api";

export function WithSSRGuest<P extends { [key: string]: any }>(
  fn: GetServerSideProps<P>
) {
  return async (
    ctx: GetServerSidePropsContext
  ): Promise<GetServerSidePropsResult<P>> => {
    const cookies = parseCookies(ctx);

    const token = cookies["@HelloMed:token"];

    if (token) {
      try {
        const api = setupApiClient(ctx);
        const response = await api.get("/v1/me");

        const { roles, type } = response.data;
        let role = "";
        if (
          roles.find((r: any) => r === "ADMIN") ||
          roles.find((r: any) => r === "MASTER")
        ) {
          role = "admin";
        } else if (
          roles.find((r: any) => r === "DOCTOR") ||
          roles.roles.find((r: any) => r === "ACCREDITED")
        ) {
          role = "doctor";
        } else {
          role = "";
        }

        return {
          redirect: {
            destination: `/${role}`,
            permanent: false,
          },
        };
      } catch {
        destroyCookie(ctx, "@HelloMed:token");
        return {
          redirect: {
            destination: "/login",
            permanent: false,
          },
        };
      }
    }

    return await fn(ctx);
  };
}
