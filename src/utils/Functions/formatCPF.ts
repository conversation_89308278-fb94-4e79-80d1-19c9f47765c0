export function formatCPF(cpf: string): string | undefined {
  let value = cpf.replace(/\D/g, "");

  if (value.length > 11) {
    value = value.slice(0, 11);
  }

  if (value.length > 9) {
    value = value.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, "$1.$2.$3-$4");
  } else if (value.length > 6) {
    value = value.replace(/^(\d{3})(\d{3})(\d{3})/, "$1.$2.$3");
  } else if (value.length > 3) {
    value = value.replace(/^(\d{3})(\d{3})/, "$1.$2");
  } else if (value.length > 0) {
    value = value.replace(/^(\d{3})/, "$1");
  }

  return value;
}
