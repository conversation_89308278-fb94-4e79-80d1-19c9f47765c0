import { format, parseISO } from 'date-fns'
import ptBR from 'date-fns/locale/pt-BR'

export function FormatDateAsHour(date?: string) {
	const newDate = !!date ? date.replace(/\s+/g, 'T') : String(new Date())

	return format(
		parseISO(newDate),
		"dd/MM/yy 'às' HH:mm",
		{
			locale: ptBR,
		}
	)
}

export const FormatDateForYearMonthDay = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd")
}

export const FormatDateForHourMinutes = (date: string) => {
	return format(parseISO(date), "HH:mm")
}

export const FormatDateForDayMonthYearUsingBars = (date?: string) => {
	const newDate = !!date ? date.replace(/\s+/g, 'T') : String(new Date())
	return format(parseISO(newDate), "dd/MM/yyyy")
}

export const FormatDateForYearMonthDayHoursSeconds = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd HH:mm:ss", { locale: ptBR })
}

export const FormatDateForYearMonthDayHoursSecondsUsingBars = (date: string) => {
	return format(parseISO(date), "dd/MM/yyyy HH:mm:ss", { locale: ptBR })
}

export const FormatDateForYearMonthDayHoursMinutes = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd HH:mm", { locale: ptBR })
}
