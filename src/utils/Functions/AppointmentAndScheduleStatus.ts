export const getStatus = (status: string): string => {
  switch (status) {
    case 'approved':
      return 'Aprovado'

    case 'did_not_attend':
      return 'Não compareceu'

    case 'realized':
      return 'Realizado'

    case 'finalized':
      return 'Finalizado'

    case 'canceled_by_patient':
      return 'Cancelado pelo Paciente'

    case 'canceled_at_patient_request':
      return 'Cancelado a pedido do Paciente'

    case 'canceled_by_backoffice':
      return 'Cancelado pelo Backoffice'

    case 'in_schedule':
      return 'Agenda'

    case 'waiting_backoffice':
      return 'Aguardando Backoffice Pontual'

    case 'waiting_backoffice_network':
      return 'Aguardando Backoffice em Rede'

    case 'budget':
      return 'Orçamento'

    case 'waiting_patient':
      return 'Aguardando <PERSON>e'

    case 'in_accreditation':
      return 'Em credenciamento'

    case 'no_contact':
      return 'Sem Contato'

    case 'no_interest':
      return 'Sem Interesse'

    case 'lack_request':
      return 'Falta Pedido'

    case 'info_divergence':
      return 'Divergência de Informações'

    case 'financial_condition':
      return 'Condição Financeira'

    case 'no_interest_accreditation':
      return 'Sem Interesse Credenciamento'

    default:
      return ''
  }
}
