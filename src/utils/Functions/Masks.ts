
export type masksProps = 'money'| 'date' | 'phone' | 'cellPhone';


export const masks = (type: masksProps, value: string) => {
	let maskedText = value ? value.replace(/\D/g, ''): '';

	switch (type) {
    case 'date':
			if (value.length === 2) {
				return maskedText;
			}
			else if (maskedText.length <= 2) {
				maskedText = maskedText.replace(/^(\d{2})/, '$1/');
			} else if (maskedText.length <= 4) {
				maskedText = maskedText.replace(/^(\d{2})(\d{0,2})/, '$1/$2');
			} else if (maskedText.length <= 6) {
				maskedText = maskedText.replace(/^(\d{2})(\d{2})(\d{0,2})/, '$1/$2/$3');
			} else {
				maskedText = maskedText.replace(/^(\d{2})(\d{2})(\d{0,4})/, '$1/$2/$3');
			}
      
      return maskedText;

    case 'money':
			const number = parseInt(value.replace(',', '').replaceAll('.', '')).toString()
			const lengthNumber = number.length
			const decimalPart = lengthNumber === 0 
				? '00' 
				: lengthNumber === 1 
					? '0' + number.slice(-2)
					: number.slice(-2)
				
					const integerPart = lengthNumber < 3 
					? '0'
				: number.slice(0, -2)

			const formattedNumber = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ".")
			const result = formattedNumber + ',' + decimalPart

			return result;
    
		case 'phone':
			if (maskedText.length > 11) {
				maskedText = maskedText.slice(0,11)
				maskedText = maskedText.replace(/^(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
			}
			if (value.length === 3) {
				return maskedText;
			} else if (maskedText.length <= 2) {
				maskedText = maskedText.replace(/^(\d{2})/, '($1)');
			} else if (maskedText.length <= 7) {
				maskedText = maskedText.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
			} else {
				maskedText = maskedText.replace(/^(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
			}

			return maskedText;

		case 'cellPhone':
			if (maskedText.length > 11) {
				maskedText = maskedText.slice(0,11)
				maskedText = maskedText.replace(/^(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
			}
			if (value.length === 3) {
				return maskedText;
			} else if (maskedText.length <= 2) {
				maskedText = maskedText.replace(/^(\d{2})/, '($1)');
			} else if (maskedText.length <= 7) {
				maskedText = maskedText.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
			} else {
				maskedText = maskedText.replace(/^(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
			}

			return maskedText;
		default:
      break;
  }
  
}
