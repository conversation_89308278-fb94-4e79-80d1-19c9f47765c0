import { utils, writeFile } from 'xlsx'
import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'

import { TableExportExcel } from '../Types/Global'

function getUniqueKeysFromObjects(objArray: any) {
	const uniqueKeys = new Set()

	for (const obj of objArray) {
		for (const key in obj) {
			if (key !== 'rowColor') {
				uniqueKeys.add(key)
			}
		}
	}

	return Array.from(uniqueKeys)
}

type ExportDataProps = {
	fileName: string
	data: TableExportExcel[]
	orientationPdf?: 'p' | 'l'
}

export const ExportDataToExcel = ({ data, fileName }: ExportDataProps) => {
	const ws = utils.json_to_sheet(data.map(({ rowColor, ...rest }) => rest))

	const wb = utils.book_new()

	utils.book_append_sheet(wb, ws, 'Sheet1')

	writeFile(wb, `${fileName}.xlsx`)
}

export const ExportDataToPDF = ({ data, fileName, orientationPdf }: ExportDataProps) => {
	const doc = new jsPDF({ orientation: orientationPdf })

	// autoTable(doc, { html: '#my-table' })
	const objectKeys = getUniqueKeysFromObjects(data.map(({ rowColor, ...rest }) => rest)) as string[]

	autoTable(doc, {
		head: [objectKeys],
		body: data.map(item => {
			const { rowColor, ...filteredItem } = item
			return Object.values(filteredItem).map(itemObject => {
				const styles = rowColor
					? { fillColor: rowColor }
					: {}

				return {
					content: itemObject,
					styles
				}
			})
		}),
	})

	doc.save(`${fileName}.pdf`)
}
