export const getAppointmentStatus = (status: string): string => {
	switch (status) {
		case 'approved':
			return 'Aprovado'

		case 'did_not_attend':
			return 'Não compareceu'

		case 'realized':
			return 'Realizado'

		case 'finalized':
			return 'Finalizado'

		case 'canceled_by_patient':
			return 'Cancelado pelo Paciente'

		case 'canceled_at_patient_request':
			return 'Cancelado a pedido do Paciente'

		case 'canceled_by_backoffice':
			return 'Cancelado pelo Backoffice'

		default:
			return ''
	}
}
