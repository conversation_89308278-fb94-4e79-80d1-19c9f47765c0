import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { masks } from "~/utils/Functions/Masks"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

type GetPatientsResponse = {
  total: number
  page: number
  lastPage: number
  perPage: number
  patients: ListUserAdmin[]
}

type GetPatientsProps = {
  page: number
  search?: string
  limit?: number

  status: 'all' | 'active' | 'inactive';
}

type GetPatientResponseAPIDTO = {
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    first_page: number;
    first_page_url: string | null;
    last_page_url: string | null;
    next_page_url: string | null;
    previous_page_url: string | null;
  }

  data: {
    secure_id: string;
    email: string;
    userInfo: {
      name: string;
      ddd_cell?: number;
      cell?: number;
      legal_document_number: string;
      birth_date: string;
    },

    partner?: {
      secureId: string;
      name: string;
    }
  }[]
}


export async function getPatientsPartner({ page, search, limit, status }: GetPatientsProps): Promise<GetPatientsResponse> {
  const response = await api.get<GetPatientResponseAPIDTO>('/v1/partner/patients-partner', {
    params: {
      search,
      page,
      limit,
      status
    }
  })

  const patients = response.data.data.map((user: any) => ({
    secure_id: user.secure_id,
    name: user.userInfo.name,
    email: user.email,
    cell: masks('cellPhone', `${user.userInfo.ddd_cell}${user.userInfo.cell}`),
    legal_document_number: user.userInfo.legal_document_number,
    birth_date: user.userInfo.birth_date,
    isActive: user.isActive,

    partner: user?.partner ? user.partner : undefined
  }))

  return {
    total: response.data.meta.total,
    perPage: response.data.meta.per_page,
    page: response.data.meta.current_page,
    lastPage: response.data.meta.last_page,
    patients
  }
}

export function usePatientsPartner({ page, search, limit, status }: GetPatientsProps) {
  return useQuery(['PatientsPartner', page, search, limit, status], () => getPatientsPartner({ page, search, limit, status }))
}
