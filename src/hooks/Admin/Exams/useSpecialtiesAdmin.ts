import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ListExamAdmin } from "~/utils/Types/Admin/Exam"

type GetExamsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	exams: ListExamAdmin[]
}

type GetExamsProps = {
	page: number
	search?: string
	limit?: number
}

export async function getExamsAdmin({ page, search, limit }: GetExamsProps): Promise<GetExamsResponse> {
	const response = await api.get('/v1/admin/exams', {
		params: {
			search,
			page,
			limit,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		exams: response.data.data
	}
}

export function useExamsAdmin({ page, search, limit }: GetExamsProps) {
	return useQuery(['ExamsAdmin', page, search, limit], () => getExamsAdmin({ page, search, limit }))
}
