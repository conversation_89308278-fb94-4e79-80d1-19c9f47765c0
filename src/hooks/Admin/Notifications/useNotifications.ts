import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { Notifications } from "~/utils/Types/Admin/Notifications";

type GetNotificationsResponse = {
  notifications: Notifications[];
};

type GetPartnersProps = {
  type: string;
  name: string;
};

export async function getNotificationsAdmin({
  type,
  name,
}: GetPartnersProps): Promise<GetNotificationsResponse> {
  const response = await api.get(`/v1/admin/template-notifications`, {
    params: {
      type,
      name,
      page: 1,
    },
  });

  return {
    notifications: response.data.data,
  };
}

export function useNotifications({ type, name }: GetPartnersProps) {
  return useQuery(["NotificationsAdmin", type, name], () =>
    getNotificationsAdmin({ type, name })
  );
}
