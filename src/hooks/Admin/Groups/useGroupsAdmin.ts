import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ListGroupAdmin } from "~/utils/Types/Admin/Group"

type GetGroupsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	groups: ListGroupAdmin[]
}

type GetGroupsProps = {
	page: number
	search?: string
	limit?: number
}

export async function getGroupsAdmin({ page, search, limit }: GetGroupsProps): Promise<GetGroupsResponse> {
	const response = await api.get('/v1/admin/groups', {
		params: {
			search,
			page,
			limit,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		groups: response.data.data
	}
}

export function useGroupsAdmin({ page, search, limit }: GetGroupsProps) {
	return useQuery(['GroupsAdmin', page, search, limit], () => getGroupsAdmin({ page, search, limit }))
}
