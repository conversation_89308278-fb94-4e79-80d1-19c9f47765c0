import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ShowUser } from "~/utils/Types/Admin/User"

type GetGroupAccreditedsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	accrediteds: ShowUser[]
}

type GetGroupAccreditedsProps = {
	page: number
	search?: string
	limit?: number
	group?: string
}

export async function getGroupAccreditedsAdminAdmin({
	page,
	search,
	limit,
	group,
}: GetGroupAccreditedsProps): Promise<GetGroupAccreditedsResponse> {
	const response = await api.get(`/v1/admin/groups/${group}/accrediteds`, {
		params: {
			search,
			page,
			limit,
		},
	})

	const accrediteds = response.data.data

	return {
		total: response.data?.meta.total,
		perPage: response.data?.meta.per_page,
		page: response.data?.meta.current_page,
		lastPage: response.data?.meta.last_page,
		accrediteds,
	}
}

export function useGroupAccreditedsAdmin({
	page,
	search,
	limit,
	group,
}: GetGroupAccreditedsProps) {
	return useQuery(["GroupAccreditedsAdmin", group, page, search, limit], () =>
		getGroupAccreditedsAdminAdmin({ page, search, limit, group })
	)
}
