import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ListSpecialtyAdmin } from "~/utils/Types/Admin/Specialty"

type GetSpecialtiesResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	specialties: ListSpecialtyAdmin[]
}

type GetSpecialtiesProps = {
	page: number
	search?: string
	limit?: number
}

export async function getSpecialtiesAdmin({ page, search, limit }: GetSpecialtiesProps): Promise<GetSpecialtiesResponse> {
	const response = await api.get('/v1/admin/specialties', {
		params: {
			search,
			page,
			limit,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		specialties: response.data.data
	}
}

export function useSpecialtiesAdmin({ page, search, limit }: GetSpecialtiesProps) {
	return useQuery(['SpecialtiesAdmin', page, search, limit], () => getSpecialtiesAdmin({ page, search, limit }))
}
