import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ListScheduleAdmin } from "~/utils/Types/Admin/Schedule"

type GetScheduleResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	schedules: ListScheduleAdmin[]
}

type GetScheduleProps = {
	page: number
	search?: string
	limit?: number
	field?: string;
	direction?: 'asc' | 'desc';
	status?: string;
	currentStatus?: string;
}

export async function getScheduleAdmin({ page, search, limit, direction, field, status, currentStatus }: GetScheduleProps): Promise<GetScheduleResponse> {
	const response = await api.get('/v1/admin/schedules', {
		params: {
			search,
			page,
			limit,
			direction,
			field: !!field ? field : undefined,
			status: (!status || status === 'all') ? undefined : [status],
			currentStatus: (!currentStatus || currentStatus === 'all') ? undefined : [currentStatus]
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		schedules: response.data.data
	}
}

export function useScheduleAdmin({ page, search, limit, direction, field, status, currentStatus }: GetScheduleProps) {
	return useQuery(['ScheduleAdmin', page, search, limit, direction, field, status, currentStatus], () => getScheduleAdmin({ page, search, limit, direction, field, status, currentStatus }))
}
