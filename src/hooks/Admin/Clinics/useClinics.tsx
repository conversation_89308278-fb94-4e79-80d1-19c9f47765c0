import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { Doctor } from "~/utils/Types/Admin/Clinic";

type GetClinicsResponse = {
  total: number;
  page: number;
  lastPage: number;
  perPage: number;
  doctors: Doctor[];
};

type GetPartnersProps = {
  page: number;
  search?: string;
  limit?: number;
  clinicId?: string;
};

export async function getClinicsAdmin({
  page,
  search,
  limit,
  clinicId,
}: GetPartnersProps): Promise<GetClinicsResponse> {
  const response = await api.get(`/v1/admin/accrediteds/${clinicId}/doctors`, {
    params: {
      search,
      page,
      limit,
    },
  });

  const doctors = response.data.data;

  return {
    total: response.data?.meta.total,
    perPage: response.data?.meta.per_page,
    page: response.data?.meta.current_page,
    lastPage: response.data?.meta.last_page,
    doctors,
  };
}

export function useClinics({
  page,
  search,
  limit,
  clinicId,
}: GetPartnersProps) {
  return useQuery(["ClinicsAdmin", page, search, limit, clinicId], () =>
    getClinicsAdmin({ page, search, limit, clinicId })
  );
}
