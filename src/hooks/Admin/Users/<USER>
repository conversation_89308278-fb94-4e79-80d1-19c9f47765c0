import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { FormatDateForDayMonthYearUsingBars } from "~/utils/Functions/FormatDates"
import { masks } from "~/utils/Functions/Masks"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

type GetUsersResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	users: ListUserAdmin[]
}

type GetUsersProps = {
	page: number
	search?: string
	limit?: number
	allowUserLogged?: boolean
}

export async function getUsersAdmin({ page, search, limit, allowUserLogged }: GetUsersProps): Promise<GetUsersResponse> {
	const response = await api.get('/v1/admin/users', {
		params: {
			search,
			page,
			limit,
			allowUserLogged
		}
	})

	const users = response.data.data.map((user: any) => ({
		secure_id: user.secure_id,
		name: user.userInfo.name,
		email: user.email,
		legal_document_number: user.userInfo.legal_document_number,
		cell: masks('cellPhone', `${user.userInfo.ddd_cell}${user.userInfo.cell}`),
	}))

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		users
	}
}

export function useUsersAdmin({ page, search, limit, allowUserLogged }: GetUsersProps) {
	return useQuery(['UsersAdmin', page, search, limit, allowUserLogged], () => getUsersAdmin({ page, search, limit, allowUserLogged }))
}
