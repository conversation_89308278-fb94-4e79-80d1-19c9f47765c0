import axios, { AxiosError } from 'axios'
import { GetServerSidePropsContext } from 'next'

import { parseCookies } from 'nookies'

import { removeCookies } from '~/contexts/AuthContext'
import { AuthTokenError } from '~/services/errors/AuthTokenError'

export function setupApiClient(ctx: GetServerSidePropsContext | undefined) {
	let cookies = parseCookies(ctx)

	const api = axios.create({
		baseURL: process.env.NEXT_PUBLIC_API_URL,
		timeout: 5 * 60 * 1000,
	})

	// api.defaults.headers.common['accept-encoding'] = '*'

	if (!!ctx?.req.headers) {
		api.defaults.headers.common['Authorization'] = `Bearer ${cookies['@HelloMed:token']}`
		api.defaults.headers.common['origin'] = ctx.req.headers.host!
	} else {
		api.defaults.headers.common['Authorization'] = `Bearer ${cookies['@HelloMed:token']}`
	}

	api.interceptors.response.use(response => {
		return response
	}, (error: AxiosError) => {
		if (error?.response?.status === 401) {
			if (!(typeof window === 'undefined')) {
				removeCookies()
			} else {
				return Promise.reject(new AuthTokenError)
			}
		}

		return Promise.reject(error)
	})

	return api
}
