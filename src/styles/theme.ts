import { extendTheme, ThemeConfig } from '@chakra-ui/react'
import { StyleFunctionProps } from "@chakra-ui/theme-tools"

const breakpoints = {
	sm: "20em", // 320px
	md: "30em", // 480px
	lg: "48em", // 768px
	xl: "62em", // 992px
	"2xl": "80em", // 1280px
}

const config: ThemeConfig = {
	initialColorMode: 'light',
	useSystemColorMode: false,
}

const textStyles = {
	headerXL: {
		fontSize: { sm: 'xl', md: '2xl' },
		fontWeight: 'bold',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText',
	},
	headerLG: {
		fontSize: { sm: 'lg', md: 'xl' },
		fontWeight: 'bold',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText',
	},
	headerMD: {
		fontSize: { sm: 'md', md: 'lg' },
		fontWeight: 'bold',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText',
	},
	headerSM: {
		fontSize: { sm: 'sm', md: 'md' },
		fontWeight: 'medium',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText',
	},
	textLG: {
		fontSize: { sm: 'md', md: 'lg' },
		fontWeight: 'normal',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText'
	},
	textMD: {
		fontSize: { sm: 'sm', md: 'md' },
		fontWeight: 'normal',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText'
	},
	textSM: {
		fontSize: { sm: 'xs', md: 'sm' },
		fontWeight: 'normal',
		lineHeight: '110%',
		letterSpacing: '-2%',
		color: 'primaryText'
	},
	textXS: {
		fontSize: { sm: 'xs', md: 'xs' },
		fontWeight: 'normal',
		lineHeight: '120%',
		letterSpacing: '-5%',
		color: 'primaryText'
	},
}

const layerStyles = {
	card: {
		bg: 'white',
		rounded: 'md',
		shadow: "md",
		color: "primaryText",
	},
	container: {
		margin: '0 auto',
		maxWidth: '65em',
	},
	tabContainer: {
		borderBottom: "1px",
		borderX: "1px",
		borderColor: "gray.100",
		borderBottomRadius: "md",
	}
}

export const theme = extendTheme({
	breakpoints,
	textStyles,
	layerStyles,

	fonts: {
		heading: 'Roboto, sans-serif',
		body: 'Roboto, sans-serif',
	},

	semanticTokens: {
		colors: {
			primary: {
				default: '#3ac1bc'
			},
			secondary: {
				default: '#0f9792'
			},
			primaryText: {
				default: '#54595f',
			},
			secondaryText: {
				default: 'white',
			},
			backgroundCard: {
				default: 'white',
			},
			backgroundSubHeader: {
				default: '#1c1c1c'
			},
			backgroundHeader: {
				default: '#4f0d28'
			}
		}
	},

	// components: {
	// 	Drawer: {
	// 		variants: {
	// 			permanent: {
	// 				dialog: {
	// 					pointerEvents: 'auto',
	// 				},
	// 				dialogContainer: {
	// 					pointerEvents: 'none',
	// 				},
	// 			},
	// 		},
	// 	},
	// },

	styles: {
		global: (props: StyleFunctionProps) => ({
			body: {
				bg: '#f2f2f2',
				color: '#54595f'
			},
			"option": {
				color: 'gray.600'
			},
			"*::placeholder": {
				color: 'gray.600',
			},
		})
	},
	config
})
