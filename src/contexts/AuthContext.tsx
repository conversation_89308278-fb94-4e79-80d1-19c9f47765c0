import { useToast } from "@chakra-ui/react";
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

import Router, { useRouter } from "next/router";
import { userAgentFromString } from "next/server";

import { AxiosResponse } from "axios";
import { BroadcastChannel } from "broadcast-channel";
import { destroyCookie, parseCookies, setCookie } from "nookies";

import { api } from "../services/apiClient";
import { UserAuthProps } from "~/utils/Types/User";
import { cancelCall } from "./VideoCallAuthContext";

type SignInCredentials = {
  email: string;
  password: string;
};

type AuthContextData = {
  isAuthenticated: boolean;
  user: UserAuthProps | undefined;
  signIn: (credentials: SignInCredentials) => Promise<any>;
  signOut: () => void;
  changeFirstAccess: () => void;
};

type AuthProviderProps = {
  children: ReactNode;
};

const AuthContext = createContext({} as AuthContextData);

let authChannel: BroadcastChannel;

function checkIsSafari() {
  return userAgentFromString(undefined).browser.name === "Safari";
}

export function removeCookies() {
  destroyCookie(undefined, "@HelloMed:token", {
    path: "/",
  });

  if (!checkIsSafari()) {
    authChannel.postMessage("signOut");
  }
}

export function signOut() {
  api.delete("v1/sessions").then(() => {
    removeCookies();
    cancelCall();
    Router.push("/");
  });
}

export function AuthProvider({ children }: AuthProviderProps) {
  const toast = useToast();
  const router = useRouter();
  const [user, setUser] = useState<UserAuthProps>();
  const isAuthenticated = !!user;

  useEffect(() => {
    if (!checkIsSafari()) {
      authChannel = new BroadcastChannel("auth");

      authChannel.onmessage = (message) => {
        switch (message.data) {
          case "signOut":
            signOut();
            break;
          default:
            break;
        }
      };
    }
  }, [router.asPath]);

  useEffect(() => {
    const { "@HelloMed:token": token } = parseCookies();

    if (token) {
      api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
      api
        .get("/v1/me")
        .then((response: AxiosResponse) => {
          const {
            permissions,
            roles,
            type,
            user: { name, email, isFirstAccess },
          } = response.data;
          setUser({
            name: name,
            email: email,
            isFirstAccess,
            permissions,
            roles,
            type,
          });
        })
        .catch(() => {
          signOut();
        });
    }
  }, [router.asPath]);

  const signIn = useCallback(async ({ email, password }: SignInCredentials) => {
    try {
      const response: AxiosResponse = await api.post("/v1/sessions", {
        email,
        password,
      });

      const {
        token,
        roles,
        type,
        user: { name, email: emailReturn, isFirstAccess },
      } = response.data;

      setCookie(undefined, "@HelloMed:token", token.token, {
        maxAge: 604800, // 7 days
        path: "/",
      });

      setUser({
        name: name,
        email: emailReturn,
        isFirstAccess,
        permissions: [],
        roles,
        type,
      });

      api.defaults.headers.common["Authorization"] = `Bearer ${token.token}`;

      if (roles.includes("ADMIN") || roles.includes("MASTER")) {
        Router.push(`/admin`);
      } else if (roles.includes("DOCTOR") || roles.includes("ACCREDITED")) {
        Router.push(`/doctor`);
      } else if (roles.includes("PARTNER")) {
        Router.push(`/partner`);
      } else {
        Router.push(`/`);
      }
    } catch (error: any) {
      toast({
        title: error?.response?.data?.message || "E-mail ou senha inválidos",
        position: "top-right",
        status: "error",
        isClosable: true,
      });
      return;
    }
  }, []);

  const changeFirstAccess = useCallback(() => {
    if (user) {
      setUser({
        ...user,
        isFirstAccess: false
      })
    }
  }, [user])

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        signIn,
        signOut,
        changeFirstAccess
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  return useContext(AuthContext);
}
