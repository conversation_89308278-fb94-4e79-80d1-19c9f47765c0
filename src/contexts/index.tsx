import { FC, ReactNode } from "react";

import { AuthProvider } from "./AuthContext";
import { VideoCallAuthProvider } from "./VideoCallAuthContext";

import { ControlFiltersProvider } from "./ControlFiltersContext";
import { SideBarDrawerProvider } from "./SideBarDrawerContext";

interface Props {
  children: ReactNode;
}

export const AppProvider: FC<Props> = ({ children }) => (
  <AuthProvider>
    <VideoCallAuthProvider>
      <SideBarDrawerProvider>
        <ControlFiltersProvider>{children}</ControlFiltersProvider>
      </SideBarDrawerProvider>
    </VideoCallAuthProvider>
  </AuthProvider>
);
