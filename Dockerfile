# Base image
FROM node:20.15.0
ARG NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ARG NEXT_PUBLIC_HOST_URL=${NEXT_PUBLIC_HOST_URL}


# Create app directory
WORKDIR /usr/src/app

# Expose port
EXPOSE 3000:3000

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package.json ./
COPY yarn.lock ./


# Install app dependencies
RUN yarn

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN yarn build

# Start the server using the production build
CMD [ "yarn", "start" ]
